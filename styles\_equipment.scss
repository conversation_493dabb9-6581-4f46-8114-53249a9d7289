// Equipment Panel Styles - Diablo 4 inspired
.equipment-panel {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  // background-color: rgba(20, 20, 25, 0.95);
  color: #e0e0e0;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
  padding: 20px;
  
  .equipment-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    
    .character-silhouette {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
      opacity: 0.7;
      width: 200px;
      height: 300px;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        filter: drop-shadow(0 0 10px rgba(100, 100, 255, 0.3));
      }
    }
    
    .equipment-slots {
      position: relative;
      width: 100%;
      height: 100%;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }
}

.equipment-slot {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(40, 40, 50, 0.7);
  border: 1px solid rgba(100, 100, 150, 0.3);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  
  &:hover {
    border-color: rgba(150, 150, 200, 0.6);
    box-shadow: 0 0 15px rgba(100, 100, 255, 0.4);
  }
  
  &.head {
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
  }
  
  &.body {
    top: 40%;
    left: 50%;
    transform: translateX(-50%);
  }
  
  &.hand {
    top: 40%;
    left: calc(50% - 100px);
  }
  
  &.feet {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
  }
  
  &.backpack {
    top: 40%;
    right: calc(50% - 100px);
  }
  
  .empty-equipment-slot {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    opacity: 0.7;
    
    .slot-icon {
      font-size: 24px;
      margin-bottom: 4px;
    }
    
    .slot-label {
      font-size: 10px;
      color: #aaa;
      text-align: center;
    }
  }
  
  .equipment-item-container {
    position: relative;
    width: 100%;
    height: 100%;
    
    .equipment-item-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.2s ease;
      
      .unequip-button {
        background: rgba(150, 30, 30, 0.8);
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 10px;
        transition: all 0.2s ease;
        
        &:hover {
          background: rgba(200, 40, 40, 0.9);
        }
      }
    }
    
    &:hover .equipment-item-overlay {
      opacity: 1;
    }
  }
}

// Inventory item overlay for drag and drop
.inventory-item-overlay {
  pointer-events: none;
  z-index: 9999;
  
  .inventory-item-inner {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(100, 100, 255, 0.5);
  }
}

// Enhance the item description popover for equipment items
.item-description-popover {
  .equipment-info {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(150, 150, 200, 0.3);
    
    .slot-type {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      
      .slot-icon {
        margin-right: 6px;
        font-size: 16px;
      }
      
      .slot-name {
        color: #aaa;
      }
    }
  }
  
  .item-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 12px;
    
    button {
      flex: 1;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 6px;


      // background: rgba(60, 60, 80, 0.8);
      color: white;
      // border: 1px solid rgba(100, 100, 150, 0.4);
      // padding: 6px 12px;
      // border-radius: 4px;
      transition: all 0.2s ease;
      
      // &:hover {
      //   background: rgba(80, 80, 100, 0.9);
      //   border-color: rgba(150, 150, 200, 0.6);
      // }
      
      &.equip-button {
        background: rgba(40, 80, 120, 0.8);
        
        &:hover {
          background: rgba(50, 100, 150, 0.9);
        }
      }
      
      // &.eat-button {
      //   background: rgba(80, 120, 40, 0.8);
        
      //   &:hover {
      //     background: rgba(100, 150, 50, 0.9);
      //   }
      // }
    }
  }
}

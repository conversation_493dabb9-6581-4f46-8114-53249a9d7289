.macos-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(3px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99996; /* Higher than any modal root */
  pointer-events: all; /* Ensure it captures all clicks */
}

.macos-modal {
  position: relative !important;
  left: auto !important;
  top: auto !important;
  max-width: 90vw;
  max-height: 90vh;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(0, 0, 0, 0.5);
  transform-origin: center center;
}

.macos-modal .window-content {
  /* padding: 20px; */
}

/* Ensure modal root is above everything else */
#modal-root {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99998;
  pointer-events: none; /* Allow clicks to pass through when no modal is open */
}

/* But modal backdrop should capture clicks */
#modal-root .macos-modal-backdrop {
  pointer-events: all;
}

/* When modal is open, disable all other interactions */
body.modal-open {
  overflow: hidden;
}

/* Ensure the modal is above all other elements */
.macos-modal-backdrop {
  isolation: isolate;
}

/* Recipe Details Modal Styles */
.recipe-details-modal {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.recipe-details-modal h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  color: #e0e0e0;
}

.recipe-details-modal .recipe-detail-icon {
  font-size: 32px;
  margin-bottom: 10px;
  text-align: center;
}

.recipe-details-modal p {
  margin-bottom: 15px;
  color: #c0c0c0;
}

.recipe-details-modal h4 {
  margin-top: 7px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #e0e0e0;
}

.recipe-details-modal .ingredient-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.recipe-details-modal .ingredient-list li {
  padding: 5px 0;
  display: flex;
  align-items: center;
}

.recipe-details-modal .ingredient-list li.has-enough {
  color: #4caf50;
}

.recipe-details-modal .ingredient-list li.not-enough {
  color: #f44336;
}

.recipe-details-modal .recipe-result {
  display: flex;
  align-items: flex-start;
  background-color: rgba(60, 60, 60, 0.5);
  padding: 10px;
  border-radius: 5px;
  margin-top: 10px;
}

.recipe-details-modal .result-icon {
  font-size: 24px;
  margin-right: 10px;
}

.recipe-details-modal .result-info {
  flex: 1;
}

.recipe-details-modal .result-name {
  font-weight: bold;
  margin-bottom: 5px;
  color: #e0e0e0;
}

.recipe-details-modal .result-description {
  font-size: 14px;
  color: #b0b0b0;
  margin-bottom: 8px;
}

.recipe-details-modal .result-stats {
  font-size: 14px;
  color: #a0a0a0;
}

.recipe-details-modal .craft-button {
  /* background-color: #2e2e2e; */
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 15px;
  transition: background-color 0.2s;
}

/* .recipe-details-modal .craft-button:hover {
  background-color: #1f1f1f;
} */

.recipe-details-modal .craft-button:disabled {
  background-color: #555;
  cursor: not-allowed;
}

/* Fishing Modal Styles */
.fishing-modal {
  text-align: center;
}

.fishing-rod-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: rgba(60, 60, 60, 0.5);
  border-radius: 5px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.fishing-rod-item:hover {
  background-color: rgba(80, 80, 80, 0.5);
}

.rod-icon {
  font-size: 24px;
  margin-right: 15px;
}

.rod-info {
  flex: 1;
  text-align: left;
}

.rod-name {
  font-weight: bold;
  margin-bottom: 5px;
  color: #e0e0e0;
}

.rod-quality {
  font-size: 14px;
  color: #b0b0b0;
}

.fishing-result-message {
  text-align: center;
  padding: 20px;
}

.fishing-result-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.fishing-result-message p {
  font-size: 18px;
  margin-bottom: 20px;
  color: #e0e0e0;
}

.fishing-result-message button {
  background-color: #202020bd;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.fishing-result-message button:hover {
  background-color: #3a7bc8;
}

import React, { useState, useCallback } from 'react';
import { MacOSModal } from './WindowManagement/MacOSModal';
import { PlacedBuilding, InventoryItemStack, StorageBuilding } from 'src/Interfaces';
import { getText } from 'src/i18n';
import { useRootStore } from 'src/stores/rootStore';
import { ItemsGrid } from './Inventory/ItemsGrid';
import { StorageGrid } from './Storage/StorageGrid';
import {
  DndContext,
  useSensor,
  useSensors,
  MouseSensor,
  TouchSensor,
  DragStartEvent,
  DragEndEvent,
  DragOverlay,
  rectIntersection,
  CollisionDetection,
} from "@dnd-kit/core";
import { snapCenterToCursor } from '@dnd-kit/modifiers';
import { InventoryItem } from './Inventory/InventoryItem';
import { Items } from 'src/enums/resources';

interface StorageModalProps {
  building: PlacedBuilding;
  isOpen: boolean;
  onClose: () => void;
  portalId?: string;
}

// Fix collision detection for proper drag highlighting
const fixCursorSnapOffset: CollisionDetection = (args) => {
  // Bail out if keyboard activated
  if (!args.pointerCoordinates) {
    return rectIntersection(args);
  }
  const { x, y } = args.pointerCoordinates;
  const { width, height } = args.collisionRect;
  const updated = {
    ...args,
    // The collision rectangle is broken when using snapCenterToCursor. Reset
    // the collision rectangle based on pointer location and overlay size.
    collisionRect: {
      width,
      height,
      bottom: y + height / 2,
      left: x - width / 2,
      right: x + width / 2,
      top: y - height / 2,
    },
  };
  return rectIntersection(updated);
};

export const StorageModal: React.FC<StorageModalProps> = ({
  building,
  isOpen,
  onClose,
  portalId
}) => {
  const [activeId, setActiveId] = useState<string | null>(null);
  const [selectedInventoryStack, setSelectedInventoryStack] = useState<InventoryItemStack | null>(null);

  // Get inventory and storage data from stores
  const itemStacks = useRootStore(state => state.itemStacks);
  const updateBuildingProperties = useRootStore(state => state.updateBuildingProperties);
  const setItemStacks = useRootStore(state => state.setItemStacks);
  // const placedBuildingsInAllRegions = useRootStore(state => state.placedBuildingsInAllRegions);
  const currRegionIndex = useRootStore(state => state.currRegionIndex);
  const storageStacks = useRootStore(state => state.placedBuildingsInAllRegions[currRegionIndex].get(building.x + '_' + building.y)?.storage?.items);
  const quantityMap = useRootStore(state => state.quantityMap);
  const setQuantityMap = useRootStore(state => state.setQuantityMap);


  if (!storageStacks) {
    console.error("No storage stacks found for building", building);
    return null;
  }

  // const storageStacks = placedBuildingsInAllRegions[currRegionIndex].get(building.x + '_' + building.y)?.storage?.items || [];

  console.log('StorageModal rendered, storageStacks', storageStacks,
    currRegionIndex, building.id);
  const storageCapacity = building.storage?.capacity || 0;


  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    })
  );

  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  }, []);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    console.log('Drag end:', { activeId, overId });

    // Parse IDs to determine source and destination
    const isActiveFromInventory = activeId.startsWith('draggable-');
    const isActiveFromStorage = activeId.startsWith('storage-draggable-');
    const isOverInventory = overId.startsWith('droppable-');
    const isOverStorage = overId.startsWith('storage-droppable-');

    console.log('Drag classification:', {
      isActiveFromInventory,
      isActiveFromStorage,
      isOverInventory,
      isOverStorage
    });

    if (isActiveFromInventory && isOverStorage) {
      // Moving from inventory to storage
      const inventoryIndex = parseInt(activeId.replace('draggable-', ''));
      const storageIndex = parseInt(overId.replace('storage-droppable-', ''));
      
      const itemStack = itemStacks[inventoryIndex];
      console.log('Moving item to storage:', { inventoryIndex, storageIndex, itemStack, storageCapacity });

      if (itemStack && storageIndex < storageCapacity) {
        // Remove from inventory
        const newItemStacks = [...itemStacks];
        newItemStacks[inventoryIndex] = null;
        setItemStacks(newItemStacks);

        const newQuantityMap = {...quantityMap};
        newQuantityMap[itemStack.itemId] -= itemStack.quantity;
        setQuantityMap(newQuantityMap);

        // Add to storage - maintain slot positions
        const newStorageStacks = [...storageStacks];

        // Remove any existing item at the target slot
        // const existingItemIndex = newStorageStacks.findIndex((item, index) =>
        //   index === storageIndex ||
        //   (item.slotIndex === undefined && newStorageStacks.indexOf(item) === storageIndex)
        // );

        // if (existingItemIndex !== -1) {
        //   newStorageStacks.splice(existingItemIndex, 1);
        // } else {
        // }
        
        // Add the item to the target storage slot
        newStorageStacks[storageIndex] = itemStack;

        console.log('Updating storage with items:', newStorageStacks);

        // Update building storage
        updateBuildingProperties(building, {
          storage: {
            capacity: storageCapacity,
            items: newStorageStacks
          }
        });
      }
    } else if (isActiveFromStorage && isOverInventory) {
      // Moving from storage to inventory
      const storageIndex = parseInt(activeId.replace('storage-draggable-', ''));
      const inventoryIndex = parseInt(overId.replace('droppable-', ''));

      const storageStack = storageStacks[storageIndex];
      if (storageStack) {
        // Add to inventory
        const newItemStacks = [...itemStacks];
        newItemStacks[inventoryIndex] = storageStack;
        console.log('222 Moving item to inventory:', { storageIndex, inventoryIndex, newItemStacks });
        setItemStacks(newItemStacks);

        const newQuantityMap = {...quantityMap};
        newQuantityMap[storageStack.itemId] += storageStack.quantity;
        setQuantityMap(newQuantityMap);

        // Remove from storage - find and remove the item at the specific slot
        const newStorageStacks = [...storageStacks];
        newStorageStacks[storageIndex] = null;

        // Update building storage
        updateBuildingProperties(building, {
          storage: {
            capacity: storageCapacity,
            items: newStorageStacks
          }
        });
      }
    } else if (isActiveFromStorage && isOverStorage) {
      const fromIndex = parseInt(activeId.replace('storage-draggable-', ''));
      const toIndex = parseInt(overId.replace('storage-droppable-', ''));

      // Swap the items
      const newStorageStacks = [...storageStacks];
      const temp = newStorageStacks[fromIndex];
      newStorageStacks[fromIndex] = newStorageStacks[toIndex];
      newStorageStacks[toIndex] = temp;
      console.log("Swapping", fromIndex, "to", toIndex, itemStacks, newStorageStacks);
      updateBuildingProperties(building, {
        storage: {
          capacity: storageCapacity,
          items: newStorageStacks
        }
      });
    } else if (isActiveFromInventory && isOverInventory) {
      const fromIndex = parseInt(activeId.replace('draggable-', ''));
      const toIndex = parseInt(overId.replace('droppable-', ''));

      // Swap the items
      const newItemStacks = [...itemStacks];
      const temp = newItemStacks[fromIndex];
      newItemStacks[fromIndex] = newItemStacks[toIndex];
      newItemStacks[toIndex] = temp;
      console.log("Swapping", fromIndex, "to", toIndex, itemStacks, newItemStacks);
      setItemStacks(newItemStacks);
    }
  }, [itemStacks, storageStacks, storageCapacity, building, setItemStacks, updateBuildingProperties]);

  // Get the currently dragged item for the overlay
  const activeItem = activeId ? (() => {
    if (activeId.startsWith('draggable-')) {
      const index = parseInt(activeId.replace('draggable-', ''));
      return itemStacks[index];
    } else if (activeId.startsWith('storage-draggable-')) {
      const index = parseInt(activeId.replace('storage-draggable-', ''));
      return storageStacks[index];
    }
    return null;
  })() : null;

  const activeItemDef = activeItem ? Items[activeItem.itemId] : null;

  return (
    <MacOSModal
      title={`${building.buildingDef.name} - ${getText('Storage')}`}
      isOpen={isOpen}
      onClose={onClose}
      initialSize={{ width: 600, height: 700 }}
      portalId={portalId}
    >
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        collisionDetection={fixCursorSnapOffset}
      >
        <div className="storage-modal">
          {/* Player Inventory Section */}
          <div className="storage-section">
            <h3>{getText('Inventory')}</h3>
            <div className="inventory-grid">
              <ItemsGrid
                itemStacks={itemStacks}
                selectedStack={selectedInventoryStack}
                setSelectedStack={setSelectedInventoryStack}
                activeId={activeId}
              />
            </div>
          </div>

          {/* Storage Section */}
          <div className="storage-section">
            <h3>{`${getText('Storage')} (${storageStacks.length}/${storageCapacity})`}</h3>
            <StorageGrid
              storageCapacity={storageCapacity}
              storageStacks={storageStacks}
              activeId={activeId}
            />
          </div>

          {/* Drag Overlay */}
          <DragOverlay modifiers={[snapCenterToCursor]} dropAnimation={null}>
            {activeItem && activeItemDef ? (
              <div className="drag-overlay-item">
                <InventoryItem itemStack={activeItem} itemDef={activeItemDef} />
              </div>
            ) : null}
          </DragOverlay>
        </div>
      </DndContext>
    </MacOSModal>
  );
};

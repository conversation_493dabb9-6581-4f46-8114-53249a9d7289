import React, { useRef, useEffect, useState, useCallback } from 'react';
import { InventoryItemStack } from 'src/Interfaces';
import { getText } from 'src/i18n';
import { useRootStore } from 'src/stores/rootStore';
import { useGroundStore } from 'src/stores/groundStore';
import { Items } from 'src/enums/resources';
import Phaser from 'phaser';

// Phaser.js types (we'll define minimal types to avoid dependency issues)
interface PhaserGame {
  destroy: (removeCanvas?: boolean) => void;
  scene: {
    add: (key: string, sceneConfig: any, autoStart?: boolean) => any;
    start: (key: string) => void;
  };
  canvas: HTMLCanvasElement;
}

interface PhaserScene {
  add: {
    graphics: () => PhaserGraphics;
    text: (x: number, y: number, text: string, style?: any) => PhaserText;
    container: (x?: number, y?: number) => PhaserContainer;
  };
  input: {
    on: (event: string, callback: Function) => void;
    off: (event: string, callback: Function) => void;
  };
  scale: {
    resize: (width: number, height: number) => void;
  };
  cameras: {
    main: {
      setBackgroundColor: (color: number) => void;
    };
  };
}

interface PhaserGraphics {
  fillStyle: (color: number, alpha?: number) => PhaserGraphics;
  lineStyle: (width: number, color: number, alpha?: number) => PhaserGraphics;
  fillRoundedRect: (x: number, y: number, width: number, height: number, radius?: number) => PhaserGraphics;
  strokeRoundedRect: (x: number, y: number, width: number, height: number, radius?: number) => PhaserGraphics;
  fillCircle: (x: number, y: number, radius: number) => PhaserGraphics;
  setInteractive: (shape?: any, callback?: Function) => PhaserGraphics;
  on: (event: string, callback: Function) => PhaserGraphics;
  setTint: (color: number) => PhaserGraphics;
  setAlpha: (alpha: number) => PhaserGraphics;
  setScale: (x: number, y?: number) => PhaserGraphics;
  x: number;
  y: number;
  destroy: () => void;
}

interface PhaserText {
  setOrigin: (x: number, y?: number) => PhaserText;
  setTint: (color: number) => PhaserText;
  x: number;
  y: number;
  destroy: () => void;
}

interface PhaserContainer {
  add: (child: any) => PhaserContainer;
  setInteractive: (shape?: any, callback?: Function) => PhaserContainer;
  on: (event: string, callback: Function) => PhaserContainer;
  setPosition: (x: number, y: number) => PhaserContainer;
  setScale: (x: number, y?: number) => PhaserContainer;
  setAlpha: (alpha: number) => PhaserContainer;
  x: number;
  y: number;
  destroy: () => void;
  list: any[];
}

// Grid configuration
interface GridConfig {
  CELL_SIZE: number;
  CELL_PADDING: number;
  INVENTORY_COLS: number;
  STORAGE_COLS: number;
  INVENTORY_ROWS: number;
  STORAGE_ROWS: number;
  TITLE_HEIGHT: number;
  SECTION_SPACING: number;
}

// Slot data interface
interface SlotData {
  type: 'inventory' | 'storage';
  index: number;
  itemStack: InventoryItemStack | null;
  container: PhaserContainer;
  background: PhaserGraphics;
}

// Drag state interface
interface DragState {
  isDragging: boolean;
  draggedItem: InventoryItemStack | null;
  sourceType: 'inventory' | 'storage' | null;
  sourceIndex: number;
  dragSprite: PhaserContainer | null;
}

export const PhaserGroundGrid: React.FC = () => {
  const gameRef = useRef<PhaserGame | null>(null);
  const sceneRef = useRef<PhaserScene | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const slotsRef = useRef<Map<string, SlotData>>(new Map());
  const dragStateRef = useRef<DragState>({
    isDragging: false,
    draggedItem: null,
    sourceType: null,
    sourceIndex: -1,
    dragSprite: null,
  });

  // Store state
  const itemStacks = useRootStore(state => state.itemStacks);
  const setItemStacks = useRootStore(state => state.setItemStacks);
  const currRegionIndex = useRootStore(state => state.currRegionIndex);
  const groundStorageStacks = useGroundStore(state => state.allRegionGroundStacks[currRegionIndex]);
  const addStackByIndex = useGroundStore(state => state.addStackByIndex);
  const removeGroundStackByIndex = useGroundStore(state => state.removeStackByIndex);
  const swapGroundStorageStacks = useGroundStore(state => state.swapStacks);
  const swapInventoryStacks = useRootStore(state => state.swapStacks);

  // Selection state
  const [selectedInventoryStack, setSelectedInventoryStack] = useState<InventoryItemStack | null>(null);
  const [selectedGroundStorageStack, setSelectedGroundStorageStack] = useState<InventoryItemStack | null>(null);

  // Responsive grid configuration
  const [screenSize, setScreenSize] = useState({ width: window.innerWidth, height: window.innerHeight });

  const getGridConfig = useCallback((): GridConfig => {
    const isMobile = screenSize.width <= 768;
    const isTablet = screenSize.width <= 1024 && screenSize.width > 768;

    return {
      CELL_SIZE: isMobile ? 35 : (isTablet ? 80 : 45),
      CELL_PADDING: isMobile ? 1 : 2,
      INVENTORY_COLS: isMobile ? 8 : 10,
      STORAGE_COLS: isMobile ? 8 : 10,
      INVENTORY_ROWS: Math.ceil(itemStacks.length / (isMobile ? 8 : 10)) + 1,
      STORAGE_ROWS: Math.ceil((groundStorageStacks?.length || 0) / (isMobile ? 8 : 10)) + 1,
      TITLE_HEIGHT: isMobile ? 30 : 40,
      SECTION_SPACING: isMobile ? 15 : 20,
    };
  }, [screenSize, itemStacks, groundStorageStacks]);

  // Helper function to get item color based on type
  const getItemColor = (itemDef: any): number => {
    const typeColors: { [key: string]: number } = {
      'food': 0x8bc34a,
      'tool': 0x795548,
      'weapon': 0xf44336,
      'material': 0x9e9e9e,
      'consumable': 0x2196f3,
    };
    return typeColors[itemDef.type?.id] || 0x666666;
  };

  // Helper function to get rarity color
  const getRarityColor = (rarity: number): number => {
    const rarityColors = [
      0x9e9e9e, // Common (gray)
      0x4caf50, // Uncommon (green)
      0x2196f3, // Rare (blue)
      0x9c27b0, // Epic (purple)
      0xffc107, // Legendary (gold)
    ];
    return rarityColors[Math.min(rarity - 1, rarityColors.length - 1)] || 0x9e9e9e;
  };

  // Create individual item slot
  const createItemSlot = useCallback((
    scene: PhaserScene,
    x: number,
    y: number,
    itemStack: InventoryItemStack | null,
    type: 'inventory' | 'storage',
    index: number,
    config: GridConfig
  ): SlotData => {
    const slotContainer = scene.add.container(x, y);
    
    // Create slot background
    const slotBg = scene.add.graphics();
    slotBg.fillStyle(0x1a1a1a);
    slotBg.lineStyle(1, 0x3a3a3a);
    slotBg.fillRoundedRect(0, 0, config.CELL_SIZE, config.CELL_SIZE, 2);
    slotBg.strokeRoundedRect(0, 0, config.CELL_SIZE, config.CELL_SIZE, 2);
    
    slotContainer.add(slotBg);

    // Add inner shadow effect
    const innerShadow = scene.add.graphics();
    innerShadow.fillStyle(0x000000, 0.3);
    innerShadow.fillRoundedRect(1, 1, config.CELL_SIZE - 2, config.CELL_SIZE - 2, 1);
    slotContainer.add(innerShadow);

    const slotData: SlotData = {
      type,
      index,
      itemStack,
      container: slotContainer,
      background: slotBg,
    };

    // Store slot data
    const slotKey = `${type}-${index}`;
    slotsRef.current.set(slotKey, slotData);

    return slotData;
  }, []);

  // Update slot visuals
  const updateSlotVisuals = useCallback((slotData: SlotData, config: GridConfig) => {
    const { container, itemStack } = slotData;

    // Remove old item visuals (keep background and shadow)
    const itemChildren = container.list.slice(2);
    itemChildren.forEach(child => child.destroy());

    if (itemStack) {
      const itemDef = Items[itemStack.itemId];
      if (itemDef) {
        // Create item icon
        const itemIcon = sceneRef.current!.add.graphics();
        const color = getItemColor(itemDef);
        itemIcon.fillStyle(color);
        itemIcon.fillRoundedRect(5, 5, 35, 35, 2);
        container.add(itemIcon);

        // Add quantity if > 1
        if (itemStack.quantity > 1) {
          const quantityBg = sceneRef.current!.add.graphics();
          quantityBg.fillStyle(0x000000, 0.8);
          quantityBg.fillCircle(config.CELL_SIZE - 12, config.CELL_SIZE - 12, 10);
          container.add(quantityBg);

          const quantityText = sceneRef.current!.add.text(
            config.CELL_SIZE - 12,
            config.CELL_SIZE - 12,
            itemStack.quantity.toString(),
            {
              fontFamily: 'Arial',
              fontSize: '10px',
              color: '#ffffff',
              fontStyle: 'bold',
            }
          );
          quantityText.setOrigin(0.5);
          container.add(quantityText);
        }

        // Add rarity border if applicable
        if (itemDef.rarity && itemDef.rarity > 1) {
          const rarityBorder = sceneRef.current!.add.graphics();
          const rarityColor = getRarityColor(itemDef.rarity);
          rarityBorder.lineStyle(2, rarityColor, 0.8);
          rarityBorder.strokeRoundedRect(4, 4, 37, 37, 3);
          container.add(rarityBorder);
        }
      }
    }
  }, [getItemColor, getRarityColor]);

  // Handle drag start
  const handleDragStart = useCallback((slotData: SlotData, pointer: any) => {
    if (!slotData.itemStack) return;

    dragStateRef.current = {
      isDragging: true,
      draggedItem: slotData.itemStack,
      sourceType: slotData.type,
      sourceIndex: slotData.index,
      dragSprite: null,
    };

    // Create drag sprite
    const dragContainer = sceneRef.current!.add.container(pointer.x, pointer.y);
    dragContainer.setScale(1.1);
    dragContainer.setAlpha(0.8);

    const dragBg = sceneRef.current!.add.graphics();
    dragBg.fillStyle(0x4a90e2, 0.8);
    dragBg.lineStyle(2, 0xffffff, 0.8);
    dragBg.fillRoundedRect(-22, -22, 44, 44, 4);
    dragContainer.add(dragBg);

    const itemDef = Items[slotData.itemStack.itemId];
    if (itemDef) {
      const itemIcon = sceneRef.current!.add.graphics();
      const color = getItemColor(itemDef);
      itemIcon.fillStyle(color);
      itemIcon.fillRoundedRect(-16, -16, 32, 32, 2);
      dragContainer.add(itemIcon);
    }

    dragStateRef.current.dragSprite = dragContainer;

    // Set selection
    if (slotData.type === 'inventory') {
      setSelectedInventoryStack(slotData.itemStack);
    } else {
      setSelectedGroundStorageStack(slotData.itemStack);
    }
  }, [getItemColor]);

  // Handle drag end
  const handleDragEnd = useCallback((targetSlotData?: SlotData) => {
    const dragState = dragStateRef.current;
    if (!dragState.isDragging || !dragState.draggedItem) return;

    // Clean up drag sprite
    if (dragState.dragSprite) {
      dragState.dragSprite.destroy();
    }

    if (targetSlotData &&
        !(dragState.sourceType === targetSlotData.type && dragState.sourceIndex === targetSlotData.index)) {

      // Handle different drop scenarios
      if (dragState.sourceType === 'inventory' && targetSlotData.type === 'storage') {
        // Move from inventory to storage
        const newItemStacks = [...itemStacks];
        newItemStacks[dragState.sourceIndex] = null;
        setItemStacks(newItemStacks);
        addStackByIndex(currRegionIndex, targetSlotData.index, dragState.draggedItem);
      } else if (dragState.sourceType === 'storage' && targetSlotData.type === 'inventory') {
        // Move from storage to inventory
        const newItemStacks = [...itemStacks];
        newItemStacks[targetSlotData.index] = dragState.draggedItem;
        setItemStacks(newItemStacks);
        removeGroundStackByIndex(currRegionIndex, dragState.sourceIndex);
      } else if (dragState.sourceType === 'storage' && targetSlotData.type === 'storage') {
        // Swap within storage
        swapGroundStorageStacks(currRegionIndex, dragState.sourceIndex, targetSlotData.index);
      } else if (dragState.sourceType === 'inventory' && targetSlotData.type === 'inventory') {
        // Swap within inventory
        swapInventoryStacks(dragState.sourceIndex, targetSlotData.index);
      }
    }

    // Reset drag state
    dragStateRef.current = {
      isDragging: false,
      draggedItem: null,
      sourceType: null,
      sourceIndex: -1,
      dragSprite: null,
    };
  }, [itemStacks, setItemStacks, addStackByIndex, currRegionIndex, removeGroundStackByIndex,
      swapGroundStorageStacks, swapInventoryStacks]);

  // Render inventory grid
  const renderInventoryGrid = useCallback((scene: PhaserScene, config: GridConfig) => {
    const totalSlots = Math.max(itemStacks.length + 5, 20);

    for (let i = 0; i < totalSlots; i++) {
      const col = i % config.INVENTORY_COLS;
      const row = Math.floor(i / config.INVENTORY_COLS);

      const x = 10 + col * (config.CELL_SIZE + config.CELL_PADDING);
      const y = config.TITLE_HEIGHT + 10 + row * (config.CELL_SIZE + config.CELL_PADDING);

      const slotData = createItemSlot(scene, x, y, itemStacks[i], 'inventory', i, config);

      // Make slot interactive
      slotData.container.setInteractive(
        new (window as any).Phaser.Geom.Rectangle(0, 0, config.CELL_SIZE, config.CELL_SIZE),
        (window as any).Phaser.Geom.Rectangle.Contains
      );

      // Add event handlers
      slotData.container.on('pointerdown', (pointer: any) => {
        if (slotData.itemStack) {
          handleDragStart(slotData, pointer);
        }
      });

      slotData.container.on('pointerup', () => {
        if (dragStateRef.current.isDragging) {
          handleDragEnd(slotData);
        }
      });

      slotData.container.on('pointerover', () => {
        if (!dragStateRef.current.isDragging && slotData.itemStack) {
          slotData.container.setScale(1.05);
          slotData.background.setTint(0xdddddd);
        } else if (dragStateRef.current.isDragging) {
          slotData.background.setTint(0x4a90e2);
        }
      });

      slotData.container.on('pointerout', () => {
        if (!dragStateRef.current.isDragging) {
          slotData.container.setScale(1.0);
          slotData.background.setTint(0xffffff);
        }
      });

      updateSlotVisuals(slotData, config);
    }
  }, [itemStacks, createItemSlot, updateSlotVisuals, handleDragStart, handleDragEnd]);

  // Render storage grid
  const renderStorageGrid = useCallback((scene: PhaserScene, config: GridConfig) => {
    if (!groundStorageStacks) return;

    const totalSlots = Math.max(groundStorageStacks.length + 5, 20);
    const inventoryHeight = config.INVENTORY_ROWS * (config.CELL_SIZE + config.CELL_PADDING);
    const storageStartY = config.TITLE_HEIGHT + 10 + inventoryHeight + config.SECTION_SPACING + config.TITLE_HEIGHT;

    for (let i = 0; i < totalSlots; i++) {
      const col = i % config.STORAGE_COLS;
      const row = Math.floor(i / config.STORAGE_COLS);

      const x = 10 + col * (config.CELL_SIZE + config.CELL_PADDING);
      const y = storageStartY + row * (config.CELL_SIZE + config.CELL_PADDING);

      const slotData = createItemSlot(scene, x, y, groundStorageStacks[i], 'storage', i, config);

      // Make slot interactive
      slotData.container.setInteractive(
        new (window as any).Phaser.Geom.Rectangle(0, 0, config.CELL_SIZE, config.CELL_SIZE),
        (window as any).Phaser.Geom.Rectangle.Contains
      );

      // Add event handlers (similar to inventory)
      slotData.container.on('pointerdown', (pointer: any) => {
        if (slotData.itemStack) {
          handleDragStart(slotData, pointer);
        }
      });

      slotData.container.on('pointerup', () => {
        if (dragStateRef.current.isDragging) {
          handleDragEnd(slotData);
        }
      });

      slotData.container.on('pointerover', () => {
        if (!dragStateRef.current.isDragging && slotData.itemStack) {
          slotData.container.setScale(1.05);
          slotData.background.setTint(0xdddddd);
        } else if (dragStateRef.current.isDragging) {
          slotData.background.setTint(0x4a90e2);
        }
      });

      slotData.container.on('pointerout', () => {
        if (!dragStateRef.current.isDragging) {
          slotData.container.setScale(1.0);
          slotData.background.setTint(0xffffff);
        }
      });

      updateSlotVisuals(slotData, config);
    }
  }, [groundStorageStacks, createItemSlot, updateSlotVisuals, handleDragStart, handleDragEnd]);

  // Initialize Phaser.js
  useEffect(() => {
    if (!containerRef.current || gameRef.current) return;

    const config = getGridConfig();
    const width = containerRef.current.clientWidth || 800;
    const height = containerRef.current.clientHeight || 600;

    // Create Phaser game instance
    const gameConfig = {
      type: Phaser.AUTO,
      width,
      height,
      parent: containerRef.current,
      backgroundColor: '#1a1a1a',
      scene: {
        create: function(this: PhaserScene) {
          sceneRef.current = this;

          // Set camera background
          this.cameras.main.setBackgroundColor(0x1a1a1a);

          // Add titles
          const titleFontSize = screenSize.width <= 768 ? 14 : 16;

          this.add.text(10, 10, getText('Inventory'), {
            fontFamily: 'Arial',
            fontSize: `${titleFontSize}px`,
            color: '#e0e0e0',
            fontStyle: 'bold',
          });

          const inventoryHeight = config.INVENTORY_ROWS * (config.CELL_SIZE + config.CELL_PADDING);
          const storageY = config.TITLE_HEIGHT + 10 + inventoryHeight + config.SECTION_SPACING;

          this.add.text(10, storageY, getText('Storage'), {
            fontFamily: 'Arial',
            fontSize: `${titleFontSize}px`,
            color: '#e0e0e0',
            fontStyle: 'bold',
          });

          // Render grids
          renderInventoryGrid(this, config);
          renderStorageGrid(this, config);

          // Handle global pointer events for drag
          this.input.on('pointermove', (pointer: any) => {
            if (dragStateRef.current.isDragging && dragStateRef.current.dragSprite) {
              dragStateRef.current.dragSprite.setPosition(pointer.x, pointer.y);
            }
          });

          this.input.on('pointerup', () => {
            if (dragStateRef.current.isDragging) {
              handleDragEnd();
            }
          });
        }
      }
    };

    gameRef.current = new (window as any).Phaser.Game(gameConfig) as PhaserGame;

    return () => {
      if (gameRef.current) {
        gameRef.current.destroy(true);
        gameRef.current = null;
        sceneRef.current = null;
        slotsRef.current.clear();
      }
    };
  }, []);

  // Handle screen resize
  useEffect(() => {
    const handleResize = () => {
      setScreenSize({ width: window.innerWidth, height: window.innerHeight });

      if (gameRef.current && containerRef.current) {
        const width = containerRef.current.clientWidth || 800;
        const height = containerRef.current.clientHeight || 600;
        gameRef.current.scale.resize(width, height);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update grids when data changes
  useEffect(() => {
    if (!sceneRef.current) return;

    // Clear existing slots
    slotsRef.current.forEach(slotData => {
      slotData.container.destroy();
    });
    slotsRef.current.clear();

    // Re-render grids
    const config = getGridConfig();
    renderInventoryGrid(sceneRef.current, config);
    renderStorageGrid(sceneRef.current, config);
  }, [itemStacks, groundStorageStacks, renderInventoryGrid, renderStorageGrid, getGridConfig]);

  // Update selection highlighting
  useEffect(() => {
    slotsRef.current.forEach(slotData => {
      const isSelected = (slotData.type === 'inventory' && selectedInventoryStack?.uuid === slotData.itemStack?.uuid) ||
                        (slotData.type === 'storage' && selectedGroundStorageStack?.uuid === slotData.itemStack?.uuid);

      if (isSelected) {
        slotData.background.setTint(0x4a90e2);
        slotData.background.setAlpha(0.8);
      } else {
        slotData.background.setTint(0xffffff);
        slotData.background.setAlpha(1);
      }
    });
  }, [selectedInventoryStack, selectedGroundStorageStack]);

  // Early return if data not ready
  if (!itemStacks || !groundStorageStacks) {
    return null;
  }

  return (
    <div
      ref={containerRef}
      className="phaser-ground-grid storage-modal"
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
        padding: '10px',
        background: 'rgba(0, 0, 0, 0.1)',
      }}
    />
  );
};

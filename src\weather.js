
export const TransitionsProbs = {
    clear: { clear: 0.7, cloudy: 0.2, snow: 0.1 },
    cloudy: { clear: 0.3, cloudy: 0.3, rainy: 0.2, foggy: 0.1, snow: 0.1 },
    rainy: { cloudy: 0.4, rainy: 0.3, stormy: 0.2, foggy: 0.1 },
    stormy: { rainy: 0.6, stormy: 0.3, cloudy: 0.1 },
    foggy: { clear: 0.2, cloudy: 0.3, foggy: 0.4, snow: 0.1 },
    snow: { clear: 0.2, cloudy: 0.3, snow: 0.4, foggy: 0.1 }
};

import { getText } from './i18n.js';

export const Weathers = {
    clear: {
        id: 'clear',
        get name() { return getText("weather_clear"); },
        icon: 'images/ui_icons/sun.png',
        get description() { return getText("weather_desc_clear"); },
        color: 'rgba(255, 255, 200, 0.1)',
        playerEffects: {},
        resourceEffects: {},
    },
    cloudy: {
        id: 'cloudy',
        get name() { return getText("weather_cloudy"); },
        icon: 'images/ui_icons/overcast.png',
        get description() { return getText("weather_desc_cloudy"); },
        color: 'rgba(200, 200, 220, 0.2)',
        playerEffects: {
            energyDrain: 0.8 // 80% of normal energy drain
        },
        resourceEffects: {
            gatherSpeed: 0.9 // 90% of normal gather speed
        }
    },
    rainy: {
        id: 'rainy',
        get name() { return getText("weather_rainy"); },
        icon: 'images/ui_icons/rain.png',
        get description() { return getText("weather_desc_rainy"); },
        color: 'rgba(100, 150, 200, 0.3)',
        playerEffects: {
            waterDrain: 0.5, // 50% of normal water drain
            energyDrain: 1.2, // 120% of normal energy drain
            moveSpeed: 0.8 // 80% of normal move speed
        },
        resourceEffects: {
            gatherSpeed: 0.7, // 70% of normal gather speed
            berryChance: 1.2, // 120% chance to find berries
            mushroomChance: 1.1 // 110% chance to find mushrooms
        },
        soundPath: `audios\\light-rain.mp3`
    },
    stormy: {
        id: 'stormy',
        get name() { return getText("weather_stormy"); },
        icon: '⛈️',
        get description() { return getText("weather_desc_stormy"); },
        color: 'rgba(50, 70, 120, 0.4)',
        playerEffects: {
            waterDrain: 0.3, // 30% of normal water drain
            energyDrain: 1.5, // 150% of normal energy drain
            moveSpeed: 0.6, // 60% of normal move speed
            healthRisk: 0.02 // 2% chance of lightning damage per minute
        },
        resourceEffects: {
            gatherSpeed: 0.5, // 50% of normal gather speed
            berryChance: 1.3, // 130% chance to find berries
            mushroomChance: 1.5 // 150% chance to find mushrooms
        },
        soundPath: `audios\\windy.mp3`
    },
    foggy: {
        id: 'foggy',
        get name() { return getText("weather_foggy"); },
        icon: '🌫️',
        get description() { return getText("weather_desc_foggy"); },
        color: 'rgba(220, 220, 220, 0.3)',
        playerEffects: {
            energyDrain: 0.7, // 70% of normal energy drain
            moveSpeed: 0.9 // 90% of normal move speed
        },
        resourceEffects: {
            gatherSpeed: 0.8, // 80% of normal gather speed
            mushroomChance: 1.4 // 140% chance to find mushrooms
        },
        soundPath: `audios\\light-rain.mp3`
    },
    snow: {
        id: 'snow',
        get name() { return getText("weather_cold"); },
        icon: 'images/ui_icons/snow.png',
        get description() { return getText("weather_desc_cold"); },
        color: 'rgba(200, 230, 255, 0.3)',
        playerEffects: {
            foodDrain: 1.6, // 160% of normal food drain
            energyDrain: 1.3, // 130% of normal energy drain
            moveSpeed: 0.8 // 80% of normal move speed
        },
        resourceEffects: {
            gatherSpeed: 0.6, // 60% of normal gather speed
            berryChance: 0.5 // 50% chance to find berries
        }
    }
};

/* Time display component styles */

#day-time-display {
    display: flex;
    justify-content: space-evenly;
    margin-bottom: 8px;
}

/* Day display */
#day-display {
    font-size: 16px;
    /* font-weight: bold; */
    /* margin-bottom: 5px; */
    text-align: center;
    /* border-right: 1px solid rgba(255, 255, 255, 0.2); */
    /* border-right: 1px solid rgb(255 255 255 / 64%); */
    /* padding-right: 5px; */
}

/* Season display */
/* #season-display {
    font-size: 14px;
    margin-bottom: 8px;
    text-align: center;
    font-weight: bold;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
} */

/* Time bar container */
#time-bar-container {
    width: 200px;
    height: 5px;
    position: relative;
    margin: 14px auto 9px auto;
    overflow: visible;
    /* border-top: 1px solid rgba(255, 255, 255, 0.2); */
    padding-inline: 9px;
    /* padding: 8px; */
}

/* Time bar */
#time-bar {
    width: 100%;
    height: 100%;
    position: relative;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
    transition: background 1s ease-in-out;
}

/* Time indicator (triangle) */
#time-indicator {
    filter: drop-shadow(0px 0px 2px rgba(0, 0, 0, 0.7));
    position: absolute;
    bottom: -9px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 8px solid #c6c5c5;
    transform: translateX(-50%);
    z-index: 10;
}

/* Digital time display */
#time-display {
    font-size: 16px;
    /* font-weight: bold; */
    /* margin-top: 10px; */
    text-align: center;
    /* display: inline; */
}

/* Clock container */
#clockContainer {
    position: fixed;
    top: 10px;
    right: 10px;
    color: white;
    font-size: 14px;
    text-shadow: 2px 2px 2px black;
    z-index: 9;
    background: rgba(40, 40, 40, 0.6);
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    width: 220px;
}

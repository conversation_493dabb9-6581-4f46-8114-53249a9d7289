


#inventoryDisplay {
  color: white;
  text-shadow: 2px 2px 2px black;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 1px;
  // width: 58%;
  width: 100%;
}

.freshness-fill {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #2ecc71; /* Default green */
  transform-origin: bottom;
  transform: scaleY(0.35); /* Initial value, will be overridden by JS */
  opacity: 0.3;
  transition:
    // transform 0.5s ease,
    background-color 0.2s ease;
  z-index: 0;
  pointer-events: none;
}

#selectedResourceInfo {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgb(255 255 255 / 65%);
  display: block;
  max-height: 40%;
}

.item-description-popover {
  background: rgba(0, 0, 0, 0.63);
  border-radius: 1px;
  padding: 13px;
  min-width: 200px;
  max-width: 250px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  color: var(--color-gray);
  // pointer-events: all;
  position: relative;

  /* Add a subtle glow effect */
  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: -1px;
  //   left: -1px;
  //   right: -1px;
  //   bottom: -1px;
  //   border-radius: 9px;
  //   background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent, rgba(255, 255, 255, 0.1));
  //   z-index: -1;
  //   pointer-events: none;
  // }

  /* Arrow styling */
  .popover-arrow {
    position: absolute;
    width: 12px;
    height: 12px;
    // background: rgb(22 22 22); /* rgba(0, 0, 0, 0.85); */
    // transform: rotate(45deg);
    z-index: -1;
    pointer-events: none;

    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1);
    background: rgb(0 0 0 / 45%);
  }

  /* Arrow positioning based on direction */
  &.arrow-top .popover-arrow {
    top: -6px;
    // transform: translateX(-50%) rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    border-left: 1px solid rgba(255, 255, 255, 0.15);
    // box-shadow: -1px -1px 3px rgba(0, 0, 0, 0.2);
    
    clip-path: polygon(50% 0%, /* 顶点（上中） */ 0% 53%, /* 左下 */ 100% 53% /* 右下 */);
  }

  &.arrow-right .popover-arrow {
    right: -6px;
    transform: translateY(-50%) rotate(45deg);
    border-right: 1px solid rgba(255, 255, 255, 0.15);
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 1px -1px 3px rgba(0, 0, 0, 0.2);
  }

  &.arrow-bottom .popover-arrow {
    bottom: -6px;
    transform: translateX(-50%) rotate(45deg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    border-right: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  &.arrow-left .popover-arrow {
    left: -6px;
    transform: translateY(-50%) rotate(45deg);
    border-left: 1px solid rgba(255, 255, 255, 0.15);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: -1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  /* Add a highlight effect when the popover appears */
  animation: popover-glow 0.2s ease-out;
}

@keyframes popover-glow {
  0% {
    box-shadow: 0 0 0 rgba(255, 255, 255, 0);
  }
  50% {
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }
}

#gatherButton {
  width: 100%;
  margin-top: 10px;
  background: rgba(0,0,0,0.7);
  color: white;
  border: 1px solid #666;
  padding: 8px 15px;
  border-radius: 5px;
  cursor: pointer;
  display: none;
}

/* Resource Display Styles */

.resource-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(45px, 1fr));
    gap: 10px;
    padding: 5px;
    max-width: 100%;
  // max-height: 30vh;
  overflow-y: auto;
  overflow-x: hidden;
  // scrollbar-width: thin;
  // scrollbar-color: #524e4e #f1f1f1;
  // scroll-behavior: smooth;
}

.resource-icon {
    // cursor: pointer;
    // cursor: url('../images/cursor/hand_small_point_n.svg'), pointer;
    text-align: center;
    position: relative;
    min-width: 0;
    /* Prevent content from forcing wider than container */
}

.resource-tooltip {
    display: none;
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 5px;
    padding: 8px;
    width: max-content;
    max-width: 200px;
    z-index: 2000;
    top: calc(100% + 5px);
    left: 0;
    margin-top: 5px;
    font-size: 12px;
    text-align: left;
}

/* Flip tooltip to the left if it would overflow on the right */
.resource-icon:nth-last-child(-n+3) .resource-tooltip {
    left: auto;
    right: 0;
}

.resource-name {
    font-weight: bold;
}

.resource-type {
    color: #aaa;
}

.resource-stats {
    color: #ccc;
    // margin-top: 4px;
    font-size: 12px;
    font-style: italic;
    margin: 5px 0
}

.no-resources {
    margin: 0;
    grid-column: 1/-1;
}

/* Add styles for the display container */
#Resources {
    display: none;
}

#Resources.active {
    display: block;
}


.inventory-container {
    margin: 0 0 10px 0;
}

.inventory-count {
    text-align: center;
    margin-bottom: 10px;
    color: #aaa;
}

.buildings-grid, .inventory-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(45px, 1fr));
    // gap: 6px;
    padding: 5px;
    min-height: 100px;
}

// .inventory-item {
//     cursor: url('cursor/small_pointer.svg') 9 9, grab;
//     user-select: none;
//     transition: transform 0.2s, opacity 0.2s;
//     position: relative;
//     aspect-ratio: 1;
//     background: rgba(0, 0, 0, 0.3);
//     border-radius: 5px;
// }

// .inventory-item.empty {
//     cursor: default;
// }

// .inventory-item-inner {
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     // height: 100%;
//     font-size: 24px;
//     padding: 5px;
//     width: 45px;
//     height: 45px;
// }

.inventory-item.dragging {
    opacity: 0.6;
    cursor: grabbing;
    background: rgba(0, 0, 0, 0.4);
}

.inventory-item.drag-over {
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    z-index: 1;
    background: rgba(255, 255, 255, 0.1);
}

.resource-icon.selected .resource-icon-inner,
.inventory-item.selected .inventory-item-inner {
    background: rgba(255, 255, 255, 0.2);
    // transform: scale(1.1);
    /* box-shadow: 0 0 6px 0px rgba(255, 255, 255, 0.5); */
}

.no-items {
    margin: 0;
    grid-column: 1/-1;
    text-align: center;
    color: #aaa;
}

.item-quantity {
    position: absolute;
    bottom: -3px;
    right: -3px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 50%;
    padding: 2px 5px;
    font-size: 12px;
    min-width: 15px;
    text-align: center;
    color: rgb(255 255 255 / 65%);
}

.item-description {
    color: #aaa;
    font-size: 11px;
    margin: 4px 0;
    font-style: italic;
}

.item-quantity-text {
    color: #aaa;
    font-size: 11px;
    margin-top: 4px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 4px;
}

.selected-resource-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.resource-icon:hover .resource-tooltip {
    display: block;
}



// .inventory-tooltip {
//   display: none;
//   position: absolute;
//   background: rgba(0, 0, 0, 0.9);
//   border-radius: 5px;
//   padding: 8px;
//   width: max-content;
//   max-width: 150px;
//   z-index: 1000;
//   top: 100%;
//   left: 50%;
//   transform: translateX(-50%);
//   margin-top: 5px;
//   font-size: 12px;
//   text-align: left;
// }

// .inventory-item:hover .inventory-tooltip {
//   display: block;
// }

// /* Hide tooltip during drag */
// .inventory-item.dragging .inventory-tooltip,
// .inventory-item.drag-over .inventory-tooltip {
//     display: none;
// }


// .inventory-item.selected {
//     background: rgba(255, 255, 255, 0.2);
//     box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
// }

// .inventory-item.selected .inventory-item-inner {
//     transform: scale(1.1);
// }












/* === BASE CELL STYLE === */
.resource-icon-inner, .inventory-item-inner {
  font-size: 24px;
  padding: 5px;
  background:
    /* Dark metallic background with subtle noise */
    linear-gradient(135deg, #1a1a1a 0%, #0d0d0d 100%),
    url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" opacity="0.1"><rect width="100" height="100" fill="none" stroke="white" stroke-width="1"/></svg>');
  border: 1px solid #3a3a3a;
  border-radius: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    inset 0 0 15px rgba(0, 0, 0, 0.7),
    0 2px 4px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  aspect-ratio: 1;
  // width: 45px;
  // height: 45px;
}

.drag-overlay {
  font-size: 24px;
  padding: 5px;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

  /* === RARITY EFFECTS === */
  /* Common (Gray) */
  .resource-icon-inner.common {
    border-color: #555;
  }
  .resource-icon-inner.common::before {
    content: "";
    position: absolute;
    inset: 0;
    background: linear-gradient(145deg, rgba(100, 100, 100, 0.1) 0%, transparent 50%);
  }

  /* Uncommon (Green - Diablo's "Magic") */
  .resource-icon-inner.uncommon {
    border-color: #3c6e3c;
  }
  .resource-icon-inner.uncommon::before {
    content: "";
    position: absolute;
    inset: 0;
    background:
      linear-gradient(145deg, rgba(60, 110, 60, 0.3) 0%, transparent 70%),
      linear-gradient(45deg, transparent 60%, rgba(100, 255, 100, 0.1) 100%);
    animation: subtle-pulse 4s infinite;
  }

  @mixin border-trail($border-color, $duration) {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border: 1px solid $border-color;
    transition: all 0.5s;
    // animation: clippath $duration infinite linear;
    border-radius: 1px;
  }

  $rare-border: rgba(100, 180, 255, 0.6);
  $epic-border: rgba(180, 100, 255, 0.8);
  $legendary-border: rgba(255, 215, 0, 0.8); // rgba(255, 200, 0, 0.9);
  $rare-dur: 9s;
  $epic-dur: 6s;
  $legendary-dur: 2s;

  /* === RARITY EFFECTS === */

  /* Rare (Blue) */
  .resource-icon-inner.rare {
    border-color: #3a5f8a;
  }
  .resource-icon-inner.rare::before {
    content: "";
    position: absolute;
    inset: 0;
    background:
      linear-gradient(145deg, rgba(40, 80, 120, 0.4) 0%, transparent 70%),
      linear-gradient(45deg, transparent 60%, rgba(100, 180, 255, 0.15) 100%);
    animation: subtle-pulse 3s infinite;
  }
  // .resource-icon-inner.rare::after {
  //   @include border-trail($rare-border, $rare-dur);
  // }

  /* Epic (Purple) */
  .resource-icon-inner.epic {
    border-color: #6a3d8a;
  }
  .resource-icon-inner.epic::before {
    content: "";
    position: absolute;
    inset: 0;
    background:
      linear-gradient(145deg, rgba(90, 50, 120, 0.5) 0%, transparent 70%),
      linear-gradient(45deg, transparent 60%, rgba(180, 100, 255, 0.2) 100%);
    animation: subtle-pulse 2s infinite;
  }
  // .resource-icon-inner.epic::after
  // /* ,.resource-icon-inner.epic::before  */
  // {
  //   /* content: "";
  //   position: absolute;
  //   bottom: -1px;
  //   left: 25%;
  //   width: 50%;
  //   height: 2px;
  //   background: linear-gradient(90deg, transparent, rgba(180, 100, 255, 0.8), transparent);
  //   animation: light-trail 2s infinite; */
  //   @include border-trail($epic-border, $epic-dur);
  // }

  /* Legendary (Gold) */
  .resource-icon-inner.legendary {
    border-color: #b8860b;
  }
  .resource-icon-inner.legendary::before {
    content: "";
    position: absolute;
    inset: 0;
    background:
      linear-gradient(145deg, rgba(150, 100, 0, 0.6) 0%, transparent 70%),
      linear-gradient(45deg, transparent 60%, rgba(255, 215, 0, 0.3) 100%);
    // animation:
    //   subtle-pulse 1.5s infinite,
    //   legendary-shimmer 6s infinite;
  }
  // .resource-icon-inner.legendary::after {
  //   @include border-trail($legendary-border, $legendary-dur);
  // }

  /* === ANIMATIONS === */
  @keyframes subtle-pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
  }

  @keyframes light-trail {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  @keyframes legendary-shimmer {
    0% { background-position: 0 0; }
    100% { background-position: 100% 100%; }
  }

  // @keyframes legendary-glow {
  //   0% { box-shadow: 0 0 5px gold; }
  //   100% { box-shadow: 0 0 20px gold; }
  // }

  /* === ITEM ICON === */
  .item-icon {
    width: 80%;
    height: 80%;
    object-fit: contain;
    filter:
      drop-shadow(0 0 2px rgba(0, 0, 0, 0.8))
      brightness(1.1);
    z-index: 1;
  }

  .resource-icon-inner:hover {
    transform: perspective(500px) rotateX(5deg) rotateY(5deg);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.7);
  }



  @keyframes clippath {
    0%,
    100% {
      clip-path: inset(0 0 98% 0);
    }

    25% {
      clip-path: inset(0 98% 0 0);
    }
    50% {
      clip-path: inset(98% 0 0 0);
    }
    75% {
      clip-path: inset(0 0 0 98%);
    }
  }
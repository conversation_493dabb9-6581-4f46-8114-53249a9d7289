import React from "react";

export const FreshnessFill = (props: {
    freshness: number, 
    totalFreshness: number
}) => {
    if (!props.freshness || !props.totalFreshness) return null;
    
    // Calculate freshness ratio and styling
    const freshnessRatio = Math.max(0, Math.min(1, props.freshness / props.totalFreshness));

    // Color based on freshness level
    let backgroundColor = '#2ecc71'; // Green for fresh
    if (freshnessRatio <= 0.2) {
        backgroundColor = '#e74c3c'; // Red for spoiling
    } else if (freshnessRatio <= 0.5) {
        backgroundColor = '#f39c12'; // Orange for getting old
    }

    const freshnessStyle = {
        transform: `scaleY(${freshnessRatio})`,
        backgroundColor: backgroundColor
    };

    return (
        <div
            className="freshness-fill"
            style={freshnessStyle}
        />
    );
};
import React, { useState } from 'react';
import { PerformantGroundGrid, GroundGridPerformanceTest } from './PerformantGroundGrid';
import { CanvasGroundGrid } from './CanvasGroundGrid';
import { PixiGroundGrid } from './pixiAndPhaser/PixiGroundGrid';
import { GroundGrid } from './GroundGrid';

/**
 * Demo component to showcase the different GroundGrid implementations
 * This component can be used for testing and comparing performance
 */
export const GroundGridDemo: React.FC = () => {
  const [currentDemo, setCurrentDemo] = useState<'switcher' | 'canvas' | 'pixi' | 'dom' | 'performance'>('switcher');

  const renderDemo = () => {
    switch (currentDemo) {
      case 'switcher':
        return (
          <div style={{ width: '100%', height: '600px' }}>
            <h3>Performant Ground Grid with Switcher</h3>
            <p>This implementation allows you to switch between different rendering methods:</p>
            <PerformantGroundGrid implementation="canvas" showSwitcher={true} />
          </div>
        );
      
      case 'canvas':
        return (
          <div style={{ width: '100%', height: '600px' }}>
            <h3>Canvas Implementation (Recommended)</h3>
            <p>High-performance HTML5 Canvas implementation with 60fps rendering:</p>
            <CanvasGroundGrid />
          </div>
        );
      
      case 'pixi':
        return (
          <div style={{ width: '100%', height: '600px' }}>
            <h3>PixiJS Implementation (WebGL)</h3>
            <p>WebGL-accelerated implementation using PixiJS:</p>
            <PixiGroundGrid />
          </div>
        );
      
      case 'dom':
        return (
          <div style={{ width: '100%', height: '600px' }}>
            <h3>DOM Implementation (Original)</h3>
            <p>Original React DOM-based implementation:</p>
            <GroundGrid />
          </div>
        );
      
      case 'performance':
        return (
          <div style={{ width: '100%', height: '800px' }}>
            <h3>Performance Testing</h3>
            <p>Compare performance between different implementations:</p>
            <GroundGridPerformanceTest />
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div style={{
      padding: '20px',
      background: 'rgba(0, 0, 0, 0.9)',
      color: 'white',
      minHeight: '100vh',
    }}>
      <h1>Ground Grid Implementation Demo</h1>
      
      {/* Navigation */}
      <div style={{
        display: 'flex',
        gap: '10px',
        marginBottom: '20px',
        flexWrap: 'wrap',
      }}>
        <button
          onClick={() => setCurrentDemo('switcher')}
          style={{
            padding: '10px 20px',
            background: currentDemo === 'switcher' ? '#4a90e2' : '#333',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px',
          }}
        >
          Switcher Demo
        </button>
        
        <button
          onClick={() => setCurrentDemo('canvas')}
          style={{
            padding: '10px 20px',
            background: currentDemo === 'canvas' ? '#4a90e2' : '#333',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px',
          }}
        >
          Canvas Only
        </button>
        
        <button
          onClick={() => setCurrentDemo('pixi')}
          style={{
            padding: '10px 20px',
            background: currentDemo === 'pixi' ? '#4a90e2' : '#333',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px',
          }}
        >
          PixiJS Only
        </button>
        
        <button
          onClick={() => setCurrentDemo('dom')}
          style={{
            padding: '10px 20px',
            background: currentDemo === 'dom' ? '#4a90e2' : '#333',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px',
          }}
        >
          DOM Only
        </button>
        
        <button
          onClick={() => setCurrentDemo('performance')}
          style={{
            padding: '10px 20px',
            background: currentDemo === 'performance' ? '#4a90e2' : '#333',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px',
          }}
        >
          Performance Test
        </button>
      </div>

      {/* Implementation Info */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        padding: '15px',
        borderRadius: '4px',
        marginBottom: '20px',
      }}>
        <h4>Implementation Comparison</h4>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          <div>
            <strong>Canvas (Recommended)</strong>
            <ul style={{ fontSize: '12px', margin: '5px 0' }}>
              <li>60fps performance</li>
              <li>Low memory usage</li>
              <li>No external dependencies</li>
              <li>Mobile-friendly</li>
            </ul>
          </div>
          <div>
            <strong>PixiJS (WebGL)</strong>
            <ul style={{ fontSize: '12px', margin: '5px 0' }}>
              <li>WebGL acceleration</li>
              <li>Rich graphics features</li>
              <li>Good for complex effects</li>
              <li>Larger bundle size</li>
            </ul>
          </div>
          <div>
            <strong>DOM (Original)</strong>
            <ul style={{ fontSize: '12px', margin: '5px 0' }}>
              <li>Easy CSS styling</li>
              <li>Familiar React patterns</li>
              <li>Poor performance at scale</li>
              <li>Heavy DOM manipulation</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Demo Content */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.05)',
        borderRadius: '4px',
        padding: '20px',
      }}>
        {renderDemo()}
      </div>

      {/* Usage Instructions */}
      <div style={{
        marginTop: '20px',
        background: 'rgba(255, 255, 255, 0.1)',
        padding: '15px',
        borderRadius: '4px',
      }}>
        <h4>Usage Instructions</h4>
        <div style={{ fontSize: '14px' }}>
          <p><strong>To use the Canvas implementation (recommended):</strong></p>
          <pre style={{ background: 'rgba(0, 0, 0, 0.5)', padding: '10px', borderRadius: '2px' }}>
{`import { CanvasGroundGrid } from 'src/components/CanvasGroundGrid';

function MyComponent() {
  return <CanvasGroundGrid />;
}`}
          </pre>
          
          <p><strong>To use the flexible switcher:</strong></p>
          <pre style={{ background: 'rgba(0, 0, 0, 0.5)', padding: '10px', borderRadius: '2px' }}>
{`import { PerformantGroundGrid } from 'src/components/PerformantGroundGrid';

function MyComponent() {
  return (
    <PerformantGroundGrid 
      implementation="canvas" 
      showSwitcher={false} 
    />
  );
}`}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default GroundGridDemo;

/*
 * From http://www.redblobgames.com/maps/mapgen2/
 * Copyright 2017 Red Blob Games <<EMAIL>>
 * License: Apache v2.0 <http://www.apache.org/licenses/LICENSE-2.0.html>
 */

import { v4 as uuidv4 } from 'uuid';
import { InventoryItemStack, Item } from "./Interfaces";
import { baseSpoilSpeed } from './settings';
import { ROT } from './enums/food_enums';

/**
 * Return value, unless it's undefined, then return orElse
 */
export function fallback(value, orElse) {
    return (value !== undefined)? value : orElse;
};

/**
 * Add several noise values together
 */
export function fbm_noise(noise, amplitudes, nx, ny) {
    let sum = 0, sumOfAmplitudes = 0;
    for (let octave = 0; octave < amplitudes.length; octave++) {
        let frequency = 1 << octave;
        sum += amplitudes[octave] * noise.noise2D(nx * frequency, ny * frequency, octave);
        sumOfAmplitudes += amplitudes[octave];
    }
    return sum / sumOfAmplitudes;
};

/**
 * Like GLSL. Return t clamped to the range [lo,hi] inclusive 
 */
export function clamp(t, lo, hi) {
    if (t < lo) { return lo; }
    if (t > hi) { return hi; }
    return t;
};

/**
 * Like GLSL. Return a mix of a and b; all a when t is 0 and all b when
 * t is 1; extrapolates when t outside the range [0,1] 
 */
export function lerp(a, b, t) {
    return a * (1.0-t) + b * t;
};

/**
 * Componentwise mix for arrays of equal length; output goes in 'out'
 */
export function lerpv(p, q, t, out=[]) {
    out.length = p.length;
    for (let i = 0; i < p.length; i++) {
        out[i] = lerp(p[i], q[i], t);
    }
    return out;
};

/**
 * Like GLSL. 
 */
export function smoothstep(a, b, t) {
    // https://en.wikipedia.org/wiki/Smoothstep
    if (t <= a) { return 0; }
    if (t >= b) { return 1; }
    t = (t - a) / (b - a);
    return (3 - 2*t) * t * t;
};

/**
 * Circumcenter of a triangle with vertices a,b,c
 */
export function circumcenter(a, b, c) {
    // https://en.wikipedia.org/wiki/Circumscribed_circle#Circumcenter_coordinates
    let ad = a[0]*a[0] + a[1]*a[1],
        bd = b[0]*b[0] + b[1]*b[1],
        cd = c[0]*c[0] + c[1]*c[1];
    let D = 2 * (a[0] * (b[1] - c[1]) + b[0] * (c[1] - a[1]) + c[0] * (a[1] - b[1]));
    let Ux = 1/D * (ad * (b[1] - c[1]) + bd * (c[1] - a[1]) + cd * (a[1] - b[1]));
    let Uy = 1/D * (ad * (c[0] - b[0]) + bd * (a[0] - c[0]) + cd * (b[0] - a[0]));
    return [Ux, Uy];
};

/**
 * Intersection of line p1--p2 and line p3--p4,
 * between 0.0 and 1.0 if it's in the line segment
 */
export function lineIntersection(x1, y1, x2, y2, x3, y3, x4, y4) {
    // from http://paulbourke.net/geometry/pointlineplane/
    let ua = ((x4-x3)*(y1-y3) - (y4-y3)*(x1-x3)) / ((y4-y3)*(x2-x1) - (x4-x3)*(y2-y1));
    let ub = ((x2-x1)*(y1-y3) - (y2-y1)*(x1-x3)) / ((y4-y3)*(x2-x1) - (x4-x3)*(y2-y1));
    return {ua, ub};
};

/**
 * in-place shuffle of an array - Fisher-Yates
 * https://en.wikipedia.org/wiki/Fisher%E2%80%93Yates_shuffle#The_modern_algorithm
 */
export function randomShuffle(array, randInt) {
    for (let i = array.length-1; i > 0; i--) {
        let j = randInt(i+1);
        let swap = array[i];
        array[i] = array[j];
        array[j] = swap;
    }
    return array;
};

function randn_bm() {
  let u = 0, v = 0;
  while(u === 0) u = Math.random(); //Converting [0,1) to (0,1)
  while(v === 0) v = Math.random();
  let num = Math.sqrt( -2.0 * Math.log( u ) ) * Math.cos( 2.0 * Math.PI * v );
  num = num / 10.0 + 0.5; // Translate to 0 -> 1
  if (num > 1 || num < 0) return randn_bm() // resample between 0 and 1
  return num
};

export function randomNormDistribution(min, max) {
    return randn_bm() * (max - min) + min;
};

// Track active overlays for proper positioning
export let activeOverlays = [];
let overlayZIndex = 99999;
const batchOverlayDelay = 200;


export function batchShowPromptOverlay(msgs) {
    for (let i = 0; i < msgs.length; i++) {
        setTimeout(() => {
            showPromptOverlay(msgs[i], i === 0);
        }, batchOverlayDelay * i);
    }
}

export function showPromptOverlay(msg, clearActiveOverlays = false) {
    // clear all existing overlays
    if (clearActiveOverlays && activeOverlays.length > 0) {
        for (let i = 0; i < activeOverlays.length; i++) {
            const overlay = activeOverlays[i];
            if (document.body.contains(overlay)) {
                document.body.removeChild(overlay);
            }
        }
        activeOverlays = [];
    }

    const successMessage = document.createElement('div');
    successMessage.textContent = msg;
    successMessage.className = 'prompt-overlay';

    // Calculate position based on existing overlays
    const overlayHeight = 40; // Approximate height of overlay including padding
    const spacing = 5; // Space between overlays
    const totalOffset = activeOverlays.length * (overlayHeight + spacing);

    // Position the overlay above previous ones
    successMessage.style.top = `calc(80% - ${totalOffset}px)`;
    successMessage.style.left = '50%';
    successMessage.style.transform = 'translate(-50%, -50%)';
    successMessage.style.zIndex = String(overlayZIndex++);

    document.body.appendChild(successMessage);

    // Add to active overlays tracking
    activeOverlays.push(successMessage);


    // Remove after 3 seconds and clean up tracking
    setTimeout(() => {
        if (document.body.contains(successMessage)) {
            document.body.removeChild(successMessage);
        }
        // Remove from active overlays array
        const index = activeOverlays.indexOf(successMessage);
        if (index > -1) {
            activeOverlays.splice(index, 1);
        }

        // Reset z-index counter if no overlays are active
        if (activeOverlays.length === 0) {
            overlayZIndex = 2000;
        }
    }, 3000);
};


export function showFadedOverlay(msg) {
    const label = document.getElementById('fadeLabel');
    label.innerHTML = msg;
    // Remove and re-add the class to restart animation
    label.classList.remove('active');
    void label.offsetWidth; // Trigger reflow
    label.classList.add('active');
};

/**
 * Generate icon iamge from a unicode character or an image url
 * if it's an image url, it will be a div that can auto resizing image
 * @param {string} icon
 */
export function genIcon(icon) {
    console.log(icon, icon.length);
    if (icon.length < 4) {
        return icon;
    } else {
        return `<div class="icon-img-div"><img src="${icon}" class="icon-img" /></div>`
    }
}



/**
 * Generate a unique ID for a placed building
 * @returns {string} - A unique ID
 */
export function generateBuildingId() {
    // return 'building_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
    return 'building_' + uuidv4();
}



export const showTooltip = (
    e, 
    self: HTMLElement,
    innerHTML,
    rect: {left: number, top: number, width: number, height: number, bottom: number, right: number} = null,
    action: () => void = null
) => {
    e.stopPropagation();

    // Create popover
    const popover = document.querySelector('#tooltip-popover');
    
    // Create content wrapper for proper sizing
    popover.querySelector('#tooltip-content').innerHTML = innerHTML;
    popover.style.display = 'block';

    if (action) {
        popover.querySelector('.panel-btn').addEventListener('click', () => {
            action();
            popover.style.display = 'none';
        }
        );
    }
    
    
    // Get dimensions
    if (!rect) {
        rect = self.getBoundingClientRect();
    }
    console.log("rect", rect);
    const popoverRect = popover.getBoundingClientRect();
    
    // Calculate positions for different sides
    const positions = {
        bottom: {
            top: rect.bottom + 10,
            left: rect.left - (popoverRect.width / 2) + (rect.width / 2)
        },
        top: {
            top: rect.top - popoverRect.height - 10,
            left: rect.left - (popoverRect.width / 2) + (rect.width / 2)
        },
        right: {
            top: rect.top + (rect.height / 2) - (popoverRect.height / 2),
            left: rect.right + 10
        },
        left: {
            top: rect.top + (rect.height / 2) - (popoverRect.height / 2),
            left: rect.left - popoverRect.width - 10
        }
    };

    // Check which position has the most space
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    
    let bestPosition = 'bottom';

    // let arrowClass = 'arrow-top';
    // // Check if bottom position would go off-screen
    // if (positions.bottom.top + popoverRect.height > windowHeight) {
    //     if (positions.top.top > 0) {
    //         bestPosition = 'top';
    //         arrowClass = 'arrow-bottom';
    //     } else if (positions.right.left + popoverRect.width < windowWidth) {
    //         bestPosition = 'right';
    //         arrowClass = 'arrow-left';
    //     } else if (positions.left.left > 0) {
    //         bestPosition = 'left';
    //         arrowClass = 'arrow-right';
    //     }
    // }

    // Ensure popover stays within window bounds
    const finalPosition = positions[bestPosition];
    finalPosition.left = Math.max(10, Math.min(windowWidth - popoverRect.width - 10, finalPosition.left));
    finalPosition.top = Math.max(10, Math.min(windowHeight - popoverRect.height - 10, finalPosition.top));

    // Apply final position and show popover
    popover.style.top = `${finalPosition.top}px`;
    popover.style.left = `${finalPosition.left}px`;
    // popover.className = `item-description-popover ${arrowClass}`;
    popover.className = 'item-description-popover';
    
    
    // // Add arrow
    // const arrow = popover.querySelector('#popover-arrow');
    // // Position arrow based on available space
    // if (bestPosition === 'bottom' || bestPosition === 'top') {
    //     const arrowLeft = rect.left - finalPosition.left + (rect.width / 2);
    //     arrow.style.left = `${arrowLeft}px`;
    // } else {
    //     const arrowTop = rect.top - finalPosition.top + (rect.height / 2);
    //     arrow.style.top = `${arrowTop}px`;
    // }

    // Close popover when clicking outside
    const closePopover = (event) => {
        // console.log("closePopover !!!!");
        if (!popover.contains(event.target) // clicked outside popover
            // && event.target !== self
        ) {
            popover.style.display = 'none';
            document.removeEventListener("click", closePopover);
        }
    };
    // setTimeout(() => document.addEventListener('click', closePopover, { once: true }), 0);
    setTimeout(() => document.addEventListener('mousedown', closePopover), 0);
};

export const initOrAddToQuantityMap = (quantityMap: {[key: string]: number}, itemDef: Item) => {
    if (!quantityMap) {
        return;
    }
    
    const quantity = itemDef.quantityMod ?? 1;
    if (quantityMap[itemDef.id] === undefined) {
        quantityMap[itemDef.id] = quantity;
    } else {
        quantityMap[itemDef.id] += quantity;
    }
};

export const addStacksToOtherStacks = (itemStacks, otherStacks, quantityMap?: {[key: string]: number}) => {
    const remainingItemStacks = [];
    //  try to find an existing stack with the same type of item
    itemStacks.forEach((itemStack) => {
        let found = false;
        for (let i = 0; i < otherStacks.length; i++) {
            if (otherStacks[i] && otherStacks[i].itemId === itemStack.itemId) {
                otherStacks[i] = {...otherStacks[i]}; // create a new object to trigger a re-render
                otherStacks[i].quantity += itemStack.quantity;
                initOrAddToQuantityMap(quantityMap, itemStack.itemDef); // update quantityMap
                // console.log("new quantity", otherStacks[i].quantity);
                found = true;
                break;
            }
        }
        if (!found) { // if not found, add to remainingItemStacks array
            remainingItemStacks.push(itemStack);
        }
    });

    if (remainingItemStacks.length === 0) {
        return;
    }
    
    //  try to find an empty slot
    let allPlaced = false;
    let itemStacksIndex = 0;
    for (let i = 0; i < otherStacks.length; i++) {
        if (!otherStacks[i]) { // if empty slot
            otherStacks[i] = remainingItemStacks[itemStacksIndex];
            initOrAddToQuantityMap(quantityMap, remainingItemStacks[itemStacksIndex].itemDef); // update quantityMap
            itemStacksIndex++;
            if (itemStacksIndex >= remainingItemStacks.length) { // all placed
                allPlaced = true;
                break;
            }
        }
    }

    if (!allPlaced) {
        const remainingItemStacks2 = remainingItemStacks.slice(itemStacksIndex);
        otherStacks.push(...remainingItemStacks2);
        remainingItemStacks2.forEach((itemStack) => {
            initOrAddToQuantityMap(quantityMap, itemStack.itemDef); // update quantityMap
        });
    }
}

export const reduceQuantityWithShallowCopy = (itemStacks, stackIndex, quantity) => {
    const stack_Copy = {...itemStacks[stackIndex]};
    stack_Copy.quantity -= quantity;
    if (stack_Copy.quantity === 0) {
        itemStacks[stackIndex] = null;
    } else {
        itemStacks[stackIndex] = stack_Copy;
    }
};


export const spoilItemStacks = (originalItemStacks: InventoryItemStack[], inGameMin: number) : InventoryItemStack[] => {
    let updated = false;
    const newStacks = originalItemStacks.map(stack => {
        if (stack && stack.freshness) {
            // has to create a new copy first
            // beacuase in InventoryItemSlot there is a custom compare function
            const newStack = {...stack};
            newStack.freshness -= inGameMin * baseSpoilSpeed;
            if (newStack.freshness <= 0) {
                newStack.itemId = "Rot";
                newStack.itemDef = ROT;
                newStack.freshness = null;
            }
            updated = true;
            return newStack;
        }

        return stack;
    });

    if (updated) {
        return newStacks;
    }

    return null;
};

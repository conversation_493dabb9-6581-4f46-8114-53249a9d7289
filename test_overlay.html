<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Overlay Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .prompt-overlay {
            position: fixed;
            /* Position is now set dynamically in JavaScript */
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            /* z-index is now set dynamically in JavaScript */
            animation: fadeOut 3s forwards;
            font-weight: bold;
        }
        
        @keyframes fadeOut {
            0% { opacity: 1; }
            70% { opacity: 1; }
            100% { opacity: 0; }
        }
        
        button {
            padding: 10px 20px;
            margin: 10px;
            font-size: 16px;
            cursor: pointer;
        }
        
        .test-container {
            text-align: center;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Overlay Positioning Test</h1>
        <p>Click the buttons to test the overlay positioning:</p>
        
        <button onclick="showSingleOverlay()">Show Single Overlay</button>
        <button onclick="showMultipleOverlays()">Show Multiple Overlays (5)</button>
        <button onclick="showRapidOverlays()">Show Rapid Overlays (10)</button>
    </div>

    <script>
        // Track active overlays for proper positioning
        let activeOverlays = [];
        let overlayZIndex = 2000;

        function showPromptOverlay(msg) {
            const successMessage = document.createElement('div');
            successMessage.textContent = msg;
            successMessage.className = 'prompt-overlay';
            
            // Calculate position based on existing overlays
            const overlayHeight = 60; // Approximate height of overlay including padding
            const spacing = 10; // Space between overlays
            const totalOffset = activeOverlays.length * (overlayHeight + spacing);
            
            // Position the overlay above previous ones
            successMessage.style.top = `calc(50% - ${totalOffset}px)`;
            successMessage.style.left = '50%';
            successMessage.style.transform = 'translate(-50%, -50%)';
            successMessage.style.zIndex = String(overlayZIndex++);
            
            // Add to active overlays tracking
            activeOverlays.push(successMessage);
            
            document.body.appendChild(successMessage);
            
            // Remove after 3 seconds and clean up tracking
            setTimeout(() => {
                if (document.body.contains(successMessage)) {
                    document.body.removeChild(successMessage);
                }
                // Remove from active overlays array
                const index = activeOverlays.indexOf(successMessage);
                if (index > -1) {
                    activeOverlays.splice(index, 1);
                }
                
                // Reset z-index counter if no overlays are active
                if (activeOverlays.length === 0) {
                    overlayZIndex = 2000;
                }
            }, 3000);
        }

        function showSingleOverlay() {
            showPromptOverlay("Single overlay test");
        }

        function showMultipleOverlays() {
            for (let i = 1; i <= 5; i++) {
                showPromptOverlay(`Overlay ${i} of 5`);
            }
        }

        function showRapidOverlays() {
            for (let i = 1; i <= 10; i++) {
                setTimeout(() => {
                    showPromptOverlay(`Rapid overlay ${i}`);
                }, i * 100); // Stagger by 100ms each
            }
        }
    </script>
</body>
</html>

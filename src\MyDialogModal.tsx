

import * as React from 'react';
import { getText } from './i18n';
import { Item } from './Interfaces';
import { MacOSModal } from './components/WindowManagement/MacOSModal';


// Fishing result modal component
// export const FishingResultModal: React.FC<{
//     icon: string;
//     message: string;
//     onClose?: () => void;
// }> = ({ icon, message, onClose }) => {
//     const [isOpen, setIsOpen] = React.useState(true);

//     const handleClose = () => {
//         setIsOpen(false);
//         if (onClose) onClose();
//     };

//     return (
//         <MacOSModal
//             title={getText('Fishing Result')}
//             isOpen={isOpen}
//             onClose={handleClose}
//             initialSize={{ width: 400, height: 300 }}
//         >
//             <div className="fishing-result-message">
//                 {icon && <div className="fishing-result-icon">{icon}</div>}
//                 <p>{message}</p>
//                 <button
//                     style={{
//                         marginTop: '10px',
//                         padding: '5px 15px',
//                         backgroundColor: '#3498db',
//                         color: 'white',
//                         border: 'none',
//                         borderRadius: '5px',
//                     }}
//                     onClick={handleClose}
//                 >
//                     {getText('OK')}
//                 </button>
//             </div>
//         </MacOSModal>
//     );
// };

// Fishing rod selection modal component
export const ChooseFishingRodModal: React.FC<{
    fishingRods: Item[];
    onSelect?: (rod: Item) => void;
}> = ({ fishingRods, onSelect }) => {
    const [isOpen, setIsOpen] = React.useState(true);

    const handleRodSelect = (rod: Item) => {
        setIsOpen(false);
        if (onSelect) {
            onSelect(rod);
        }
    };

    return (
        <MacOSModal
            title={getText('Select Fishing Rod')}
            isOpen={isOpen}
            onClose={() => setIsOpen(false)}
            initialSize={{ width: 450, height: 400 }}
        >
            <div className="fishing-rod-selection">
                {fishingRods.map((rod, index) => (
                    <div
                        key={index}
                        className="fishing-rod-item"
                        onClick={() => handleRodSelect(rod)}
                    >
                        <div className="rod-icon">{rod.icon}</div>
                        <div className="rod-info">
                            <div className="rod-name">{rod.name}</div>
                            <div className="rod-quality">Quality: {rod.rarity || 'common'}</div>
                        </div>
                    </div>
                ))}
            </div>
        </MacOSModal>
    );
};



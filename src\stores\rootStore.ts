import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import { Building, InventoryItemStack, Item, ItemToConsume, PlacedBuilding, ResourceMetaInfo, Boat } from "src/Interfaces";
import { DEFAULT_INV_SLOT, GRID_HEIGHT, GRID_WIDTH, MockedItemStacks } from "src/settings";
import { addStacksToOtherStacks, generateBuildingId, initOrAddToQuantityMap, spoilItemStacks } from "src/util";
import { startProgressBar } from 'src/gameinfo';
// import { BUILDINGS } from 'src/enums/building_enums';
import { getText } from 'src/i18n';
import { Items } from 'src/enums/resources';
import { useGroundStore } from './groundStore';


interface RootState {
    // State
    quantityMap: {[key: string]: number};
    itemStacks: InventoryItemStack[];
    placedBuildingsInAllRegions: Map<string, PlacedBuilding>[];
    prevRegionIndex: number | null;
    currRegionIndex: number | null;
    resourceMetaInfosInAllRegions: ResourceMetaInfo[][];
    biomesList: string[];
    player: {
        speed: number;
        waterResistance: number;
        insulation: number;
        thermalShield: number;
    };
    // backpackSlot: number;
    // selectedBuildingToPlace: Building | null;

    // Actions
    setQuantityMap: (quantityMap: {[key: string]: number}) => void;
    // setBackpackSlot: (backpackSlot: number) => void;
    setItemStacks: (itemStacks: InventoryItemStack[]) => void;
    updatePlacedBuildingsInARegion: (regionIndex: number, newPlacedBuildings: Map<string, PlacedBuilding>) => void;
    // initializeRegion: (regionIndex: number) => void;
    addItemToInventory: (itemDef: Item) => void;
    removeItemsFromInventory: (itemsToRemove: ItemToConsume[]) => void;
    spoilInventory: (inGameMin: number) => void;
    setResourceMetaInfosInAllRegions: (resourceMetaInfos: ResourceMetaInfo[][]) => void;
    setPrevRegionIndex: (regionIndex: number) => void;
    setCurrRegionIndex: (regionIndex: number) => void;
    setBiomesList: (biomesList: string[]) => void;
    placeBuildingAtRegion: (x: number, y: number, selectedBuildingToPlace: Building) => void;
    updateBuildingProperties: (building: PlacedBuilding, properties: Partial<PlacedBuilding>) => void;
    setBoat: (boat: Boat | null) => void;
    removeBuildingFromRegion: (buildingId: string) => void;
    swapStacks: (fromIndex: number, toIndex: number) => void;

    removeSelectedInventoryStackByUuid: (uuid: string) => void;
    addStackToInventory: (itemStack: InventoryItemStack) => void;

    startProgressBar: (title: string, actionDurationInGameMin: number, doneCallback: () => void) => void;
}

// Create the store
export const useRootStore = create<RootState>((set, get) => ({
    quantityMap: InitialQuantityMap,
    itemStacks: [],
    placedBuildingsInAllRegions: [], // TODO: remove this as this is not used anymore
    prevRegionIndex: null,
    currRegionIndex: 0,
    resourceMetaInfosInAllRegions: [],
    biomesList: [],
    player: {
        speed: 1,
        waterResistance: 0,
        insulation: 0,
        thermalShield: 0
    },
    // backpackSlot: 0,

    startProgressBar: (title, actionDurationInGameMin, doneCallback) => {
        startProgressBar(title, actionDurationInGameMin, () => {
            const { spoilInventory } = get();
            spoilInventory(actionDurationInGameMin);
            useGroundStore.getState().spoilGroundStacks(actionDurationInGameMin);
            doneCallback();
        });
    },

    setQuantityMap: (quantityMap) => set({ quantityMap }),

    // setBackpackSlot: (backpackSlot) => set({ backpackSlot }),

    setBiomesList: (biomesList) => set({ biomesList }),

    // Actions
    setItemStacks: (itemStacks) => set({ itemStacks }),

    removeSelectedInventoryStackByUuid: (uuid) => set((state) => {
        const index = state.itemStacks.findIndex(stack => stack?.uuid === uuid);
        const newStacks = [...state.itemStacks];
        newStacks[index] = null;
        return { itemStacks: newStacks };
    }),

    updatePlacedBuildingsInARegion: (regionIndex, newPlacedBuildings) => set((state) => {
        const newPlacedBuildingsInAllRegions = [...state.placedBuildingsInAllRegions];
        newPlacedBuildingsInAllRegions[regionIndex] = newPlacedBuildings;
        return { placedBuildingsInAllRegions: newPlacedBuildingsInAllRegions };
    }),

    // initializeRegion: (regionIndex) => set((state) => {
    //     const newPlacedBuildingsInAllRegions = [...state.placedBuildingsInAllRegions];
    //     const newPlacedBuildingsMap = new Map<string, PlacedBuilding>();
    //     let treeDef = BUILDINGS.TREE;
    //     let treePropbability = 0.3;
    //     let stonePropbability = 0.05;
    //     if (state.biomesList[regionIndex] == 'BEACH') {
    //         treeDef = BUILDINGS.coconut_tree;
    //         treePropbability = 0.1;
    //         stonePropbability = 0.01;
    //     }
    //     const stonePropbabilityOffset = 1 - stonePropbability;

    //     for (let i = 0; i < GRID_WIDTH; i++) {
    //         for (let j = 0; j < GRID_HEIGHT; j++) {
    //             const randomNum = Math.random();
    //             if (randomNum < treePropbability) {
    //                 newPlacedBuildingsMap.set(`${i}_${j}`, {
    //                     id: uuidv4(),
    //                     buildingDef: treeDef,
    //                     x: i,
    //                     y: j
    //                 });
    //             } else if (randomNum > stonePropbabilityOffset) {
    //                 newPlacedBuildingsMap.set(`${i}_${j}`, {
    //                     id: uuidv4(),
    //                     buildingDef: BUILDINGS.STONE,
    //                     x: i,
    //                     y: j
    //                 });
    //             }
    //         }
    //     }
    //     newPlacedBuildingsInAllRegions[regionIndex] = newPlacedBuildingsMap;
    //     return { placedBuildingsInAllRegions: newPlacedBuildingsInAllRegions };
    // }),

    // TODO FIX inventory use weight
    addItemToInventory: (itemDef: Item) => {
        const addQuantity = itemDef.quantityMod;
        let updated = false;

        set((state) => {
            // First try to add to an existing stack
            const newStacks = state.itemStacks.map(stack => {
                if (stack && stack.itemId === itemDef.id) {
                    const newExistingStack = {...stack};
                    newExistingStack.quantity += addQuantity;
                    if (itemDef.freshness) {
                        newExistingStack.freshness = (itemDef.freshness + newExistingStack.freshness * newExistingStack.quantity) / (newExistingStack.quantity + addQuantity);
                    } else if (itemDef.maxDurability) {
                        newExistingStack.durability = (itemDef.maxDurability + newExistingStack.durability * newExistingStack.quantity) / (newExistingStack.quantity + addQuantity);
                    }
                    initOrAddToQuantityMap(state.quantityMap, itemDef);
                    updated = true;
                    return newExistingStack;
                }
                return stack;
            });

            if (updated) {
                return { itemStacks: newStacks, quantityMap: {...state.quantityMap} };
            }
        });
        

        if (!updated) {
            const newStack = {
                itemId: itemDef.id,
                freshness: itemDef.freshness,
                quantity: addQuantity,
                uuid: uuidv4(),
                itemDef: itemDef
            };
            const { addStackToInventory } = get();
            addStackToInventory(newStack);
        }
    },

    addStackToInventory: (itemStack: InventoryItemStack) => set((state) => {
        const newStacks = [...state.itemStacks];

        addStacksToOtherStacks([itemStack], newStacks, state.quantityMap);

        return { itemStacks: newStacks, quantityMap: {...state.quantityMap} };
    }),


    // TODO FIX check if enough. note the stack can not be splitted. so we actually do not need quantityMap
    removeItemsFromInventory: (itemsToRemove: ItemToConsume[]) => set((state) => {
        // for (const itemToRemove of itemsToRemove) {
        //     if (state.quantityMap[itemToRemove.itemDef.id] < itemToRemove.quantity) {
        //         alert('Not enough items in inventory!');
        //         return {}; // No change if not enough items
        //     }
        // }
        
        let updated = false;
        const newStacks = state.itemStacks.map(stack => {
            if (!stack) {
                return stack;
            }

            const matchingItemToRemove = itemsToRemove.find(itemToRemove => (stack.uuid === itemToRemove.uuid || (itemToRemove.uuid == null && stack.itemId === itemToRemove.itemDef.id)));
            if (!matchingItemToRemove || matchingItemToRemove.quantity > stack.quantity) {
                return stack;
            }

            updated = true;
            state.quantityMap[matchingItemToRemove.itemDef.id] -= matchingItemToRemove.quantity;
            // const matchingItemToRemove = itemsToRemove[matchingIndex];
            if (stack.quantity > matchingItemToRemove.quantity) {
                return {...stack, quantity: stack.quantity - matchingItemToRemove.quantity};
            } else {
                matchingItemToRemove.quantity -= stack.quantity;
                return null;
            }

        });

        if (!updated) {
            alert('Not enough items in inventory!');
            return {}; // No change if not enough items
        }


        return { itemStacks: newStacks, quantityMap: {...state.quantityMap} };
    }),

    swapStacks: (fromIndex, toIndex) => set((state) => {
        const newStacks = [...state.itemStacks];
        const temp = newStacks[fromIndex];
        newStacks[fromIndex] = newStacks[toIndex];
        newStacks[toIndex] = temp;
        return { itemStacks: newStacks };
    }),

    spoilInventory: (inGameMin) => set((state) => {
        const newStacks = spoilItemStacks(state.itemStacks, inGameMin);

        if (newStacks) {
            return { itemStacks: newStacks };
        }

        return {}; // No change if nothing updated
    }),

    setResourceMetaInfosInAllRegions: (resourceMetaInfos) => set({
        resourceMetaInfosInAllRegions: resourceMetaInfos
    }),

    setPrevRegionIndex: (regionIndex) => set({ prevRegionIndex: regionIndex }),

    setCurrRegionIndex: (regionIndex) => {
        // const { placedBuildingsInAllRegions, initializeRegion } = get();

        // if (placedBuildingsInAllRegions[regionIndex] === undefined) {
        //     initializeRegion(regionIndex);
        // }

        set({ currRegionIndex: regionIndex });
    },

    // TODO FIX use set for whole
    updateBuildingProperties: (building, properties) => {
        const { currRegionIndex, placedBuildingsInAllRegions } = get();

        if (currRegionIndex === null) return;

        const placedBuildingsMap = placedBuildingsInAllRegions[currRegionIndex];

        if (!placedBuildingsMap) {
            console.error("No placed buildings map for current region");
            return;
        }

        // Find all grid cells that contain this building
        const buildingCells: string[] = [];
        placedBuildingsMap.forEach((placedBuilding, key) => {
            if (placedBuilding.id === building.id) {
                buildingCells.push(key);
            }
        });

        if (buildingCells.length === 0) {
            console.error("Building not found in the current region");
            return;
        }

        // Update the building with new properties
        set((state) => {
            const newPlacedBuildingsInAllRegions = [...state.placedBuildingsInAllRegions];
            const updatedPlacedBuildingsMap = new Map(newPlacedBuildingsInAllRegions[currRegionIndex]);

            // Create updated building object
            const updatedBuilding = { ...building, ...properties };

            // Update all cells that contain this building
            buildingCells.forEach(cell => {
                updatedPlacedBuildingsMap.set(cell, updatedBuilding);
            });

            newPlacedBuildingsInAllRegions[currRegionIndex] = updatedPlacedBuildingsMap;
            return { placedBuildingsInAllRegions: newPlacedBuildingsInAllRegions };
        });
    },

    placeBuildingAtRegion: (x, y, selectedBuildingToPlace) => {
        if (!selectedBuildingToPlace) return;

        const { quantityMap, currRegionIndex, placedBuildingsInAllRegions, removeItemsFromInventory } = get();

        if (currRegionIndex === null) return;

        const placedBuildingsMap = placedBuildingsInAllRegions[currRegionIndex];

        if (!placedBuildingsMap) {
            throw new Error("No placed buildings map for current region");
        }

        // Check if building fits within grid bounds
        if (x + selectedBuildingToPlace.width > GRID_WIDTH ||
            y + selectedBuildingToPlace.height > GRID_HEIGHT) {
            return;
        }

        if (placedBuildingsMap) {
            for (let i = x; i < x + selectedBuildingToPlace.width; i++) {
                for (let j = y; j < y + selectedBuildingToPlace.height; j++) {
                    // Check if a cell is already occupied
                    if (placedBuildingsMap.has(`${i}_${j}`)) {
                        return;
                    }
                }
            }
        }

        if (!selectedBuildingToPlace.resources.every(resource => quantityMap[resource.itemDef.id] >= resource.quantity)) {
            alert("Not enough resources");
            return;
        }

        console.log("Building placed at x: " + x + ", y: " + y);

        startProgressBar(getText('Building...'), 20, () => {
            // Create a new placed building
            const placedBuilding: PlacedBuilding = {
                id: generateBuildingId(),
                buildingDef: selectedBuildingToPlace,
                x: x,
                y: y,
            };

            // Add storage if building has storage capability
            if ('storageCapacity' in selectedBuildingToPlace) {
                const storageCapacity = (selectedBuildingToPlace as any).storageCapacity;
                placedBuilding.storage = {
                    items: new Array(storageCapacity).fill(null),
                    capacity: storageCapacity
                };
            }

            set((state) => {
                const newPlacedBuildingsInAllRegions = [...state.placedBuildingsInAllRegions];
                const updatedPlacedBuildingsMap = newPlacedBuildingsInAllRegions[currRegionIndex];

                for (let i = x; i < x + selectedBuildingToPlace.width; i++) {
                    for (let j = y; j < y + selectedBuildingToPlace.height; j++) {
                        updatedPlacedBuildingsMap.set(`${i}_${j}`, placedBuilding);
                    }
                }

                return { placedBuildingsInAllRegions: newPlacedBuildingsInAllRegions };
            });

            removeItemsFromInventory(selectedBuildingToPlace.resources);
            console.log("removeItemsFromInventory(selectedBuildingToPlace.resources)", selectedBuildingToPlace.resources);
        });
    },

    removeBuildingFromRegion: (buildingId: string) => {
        const { currRegionIndex, placedBuildingsInAllRegions } = get();

        if (currRegionIndex === null) return;

        const placedBuildingsMap = placedBuildingsInAllRegions[currRegionIndex];

        if (!placedBuildingsMap) {
            console.error("No placed buildings map for current region");
            return;
        }

        startProgressBar(getText("Working..."), 50, () => {
            set((state) => {
                const newPlacedBuildingsInAllRegions = [...state.placedBuildingsInAllRegions];
                const updatedPlacedBuildingsMap = newPlacedBuildingsInAllRegions[currRegionIndex];

                updatedPlacedBuildingsMap.forEach((placedBuilding, key) => {
                    if (placedBuilding.id == buildingId) {
                        updatedPlacedBuildingsMap.delete(key);
                    }
                });

                return { placedBuildingsInAllRegions: newPlacedBuildingsInAllRegions };
            });
        });
    },

    setBoat: (boat) => set((state) => ({
        boat: {
            ...state.boat,
            currentBoat: boat
        }
    })),
}));


const InitialQuantityMap = MockedItemStacks.reduce(function(map, obj) {
    map[obj.itemId] = obj.quantity;
    return map;
}, {});
const initialItemStacks = new Array(DEFAULT_INV_SLOT).fill(null);
MockedItemStacks.forEach((stack, index) => {
    stack.itemDef = Items[stack.itemId];
    initialItemStacks[index] = stack;
});
useRootStore.getState().setQuantityMap(InitialQuantityMap);
useRootStore.getState().setItemStacks(initialItemStacks);

// // Optional: Subscribe to store changes to save to localStorage
// useStore.subscribe(
//   (state) => localStorage.setItem('app-state', JSON.stringify(state))
// );
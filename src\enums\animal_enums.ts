import { getText } from "src/i18n";
import { ResourceTypes } from "./common_enum";
import { Meat } from "src/Interfaces";
import { MATERIALS } from "./Materials";


export const MEAT: { [key: string]: Meat } = {
    "PoultryMeat": {
        id: "PoultryMeat",
        get name() { return getText("PoultryMeat"); },
        get description() { return getText("desc_PoultryMeat"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "images/meat/poultryLeg.png",
        "food": 10,
        "water": 1,
        "energy": 1,
        "health": 1,
        meatVal: 1
    },
    "Pork": {
        id: "Pork",
        get name() { return getText("Pork"); },
        get description() { return getText("desc_Pork"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🍖",
        "food": 20,
        "water": 0,
        "energy": 10,
        "health": 5,
        meatVal: 1.5
    },
    "Beef": {
        id: "Beef",
        get name() { return getText("Beef"); },
        get description() { return getText("desc_Beef"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "images/meat/steak.png",
        "food": 20,
        "energy": 1,
        "health": 1,
        meatVal: 2
    },
    // 羊肉
    "Mutton": {
        id: "Mutton",
        get name() { return getText("Mutton"); },
        get description() { return getText("desc_Mutton"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🐏",
        "food": 20,
        "energy": 1,
        "health": 1,
        meatVal: 1.5
    },
    "Venison": {
        id: "Venison",
        get name() { return getText("Venison"); },
        get description() { return getText("desc_Venison"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🦌",
        "food": 10,
        "health": 1,
        meatVal: 1
    },
    "RabbitMeat": {
        id: "RabbitMeat",
        get name() { return getText("RabbitMeat"); },
        get description() { return getText("desc_"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🐇",
        "food": 10,
        "health": 1,
        meatVal: 0.5
    }
};

export const DEAD_ANIMALS = {
    "Dead Chicken": {
        "id": "Dead Chicken",
        get name() { return getText("Dead Chicken");},
        get description() { return getText("Dead Chicken"); },
        "type": ResourceTypes.Harvestable,
        "icon": "🐔",
        "freshness": 2
    },
    "DeadRabbit": {
        "id": "DeadRabbit",
        get name() { return getText("Dead Rabbit");},
        get description() { return getText("Dead Rabbit"); },
        "type": ResourceTypes.Harvestable,
        "icon": "🐇",
        harvestRes: [
            { itemDef: MATERIALS.Rawhide, quantity: 1 },
            { itemDef: MEAT.RabbitMeat, quantity: 2 }
        ],
        "freshness": 2
    },
    "Dead Deer": {
        "id": "Dead Deer",
        get name() { return getText("Dead Deer");},
        get description() { return getText("Dead Deer"); },
        "type": ResourceTypes.Harvestable,
        "icon": "🦌",
        "freshness": 200
    },
    "Dead Boar": {"id": "Dead Boar",
        get name() { return getText("Dead Boar");},
        get description() { return getText("Dead Boar"); },
        "type": ResourceTypes.Harvestable,
        "icon": "🐗",
        "freshness": 200
    },
};

export const ANIMALS = {
    "Rabbit": {
        id: "Rabbit",
        get name() { return getText("Rabbit"); },
        get description() { return getText("desc_Rabbit"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🐇",
        harvestRes: [
            { itemDef: DEAD_ANIMALS.DeadRabbit, quantity: 1 }
        ],
        "freshness": 20
    },
    "Chicken": {
        id: "Chicken",
        get name() { return getText("Chicken"); },
        get description() { return getText("desc_Chicken"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "images/animals/chicken.png",
        "freshness": 20
    },
    "Turtle": {
        id: "Turtle",
        get name() { return getText("Turtle"); },
        get description() { return getText("desc_Turtle"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🐢",
        "freshness": 20
    }
};



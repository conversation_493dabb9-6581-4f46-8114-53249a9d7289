import { getText } from "src/i18n";
import { RARITY, ResourceTypes } from "./common_enum";
import { MATERIALS } from "./Materials";
import { FISHING_RODS } from "./fishing_enums";
import { BackPack, CraftableItem, CraftableTool, EquipableItem, SlotType } from "src/Interfaces";

interface EquipmentSlotTypes {
    BACKPACK: SlotType;
    HEAD: SlotType;
    BODY: SlotType;
    HAND: SlotType;
    FEET: SlotType;
}

const EQUIPMENT_SLOT_TYPES: EquipmentSlotTypes = {
    HEAD: {
        id: 'HEAD',
        get name() { return getText("equipable_slot_head"); },
        icon: "🧢",
        get description() { return getText("equipable_slot_head_desc"); },
    },
    BODY: {
        id: 'BODY',
        get name() { return getText("equipable_slot_body"); },
        icon: "🧥",
        get description() { return getText("equipable_slot_body_desc"); },
    },
    BACKPACK: {
        id: 'BAC<PERSON><PERSON>CK',
        get name() { return getText("equipable_slot_backpack"); },
        icon: "🎒",
        get description() { return getText("equipable_slot_backpack_desc"); },
    },
    HAND: {
        id: 'HAND',
        get name() { return getText("equipable_slot_hand"); },
        icon: "✋",
        get description() { return getText("equipable_slot_hand_desc"); },
    },
    FEET: {
        id: 'FEET',
        get name() { return getText("equipable_slot_feet"); },
        icon: "Sock",
        get description() { return getText("equipable_slot_feet_desc"); },
    }
};

const SmallBackpack = {
    id: 'SmallBackpack',
    get name() { return getText("item_small_backpack"); },
    "type": ResourceTypes.Equipable,
    "icon": "images/equips/backpack.png",
    get description() { return getText("desc_small_backpack"); },
    storageCapacity: 20,
    "slotType": EQUIPMENT_SLOT_TYPES.BACKPACK,
    ingredients: [
        { itemDef: MATERIALS.Grass, quantity: 1 },
    ],
};

const MediumBackpack = {
    id: 'MediumBackpack',
    get name() { return getText("item_medium_backpack"); },
    "type": ResourceTypes.Equipable,
    "icon": "🎒",
    get description() { return getText("desc_medium_backpack"); },
    storageCapacity: 40,
    "slotType": EQUIPMENT_SLOT_TYPES.BACKPACK,
    ingredients: [
        { itemDef: SmallBackpack, quantity: 1 },
        { itemDef: MATERIALS.Grass, quantity: 1 },
    ],
};

const LargeBackpack = {
    id: 'LargeBackpack',
    get name() { return getText("item_large_backpack"); },
    "type": ResourceTypes.Equipable,
    "icon": "🎒",
    get description() { return getText("desc_large_backpack"); },
    "storageCapacity": 60,
    "slotType": EQUIPMENT_SLOT_TYPES.BACKPACK,
    ingredients: [
        { itemDef: MediumBackpack, quantity: 1 },
        { itemDef: MATERIALS.Grass, quantity: 1 },
    ],
};

export const BACKPACKS: { [key: string]: BackPack} = {
    "SmallBackpack": SmallBackpack,
    "MediumBackpack": MediumBackpack,
    "LargeBackpack": LargeBackpack,
};

export const CRAFTABLE_EQUIPABLES: { [key: string]: EquipableItem} = {
    ...BACKPACKS,
    Spear: {
        id: 'Spear',
        get name() { return getText("Spear"); },
        get description() { return getText("desc_spear"); },
        "type": ResourceTypes.Equipable,
        "slotType": EQUIPMENT_SLOT_TYPES.HAND,
        icon: '(images/spear.svg)',
        "rarity": RARITY.COMMON,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: MATERIALS.Log, quantity: 1 },
        ],
        "time": 30
    },
    Umbrella: {
        id: 'Umbrella',
        get name() { return getText("Umbrella"); },
        get description() { return getText("desc_umbrella"); },
        "type": ResourceTypes.Equipable,
        "slotType": EQUIPMENT_SLOT_TYPES.HAND,
        icon: 'images/equips/umbrella.png',
        "rarity": RARITY.COMMON,
        "ingredients": [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Grass, quantity: 1 },
        ],
        "time": 30
    },
};

export const CRAFTABLE_MATERIALS: { [key: string]: CraftableItem} = {
   "Rope": {
        id: 'Rope',
        get name() { return getText("Rope"); },
        get description() { return getText("desc_rope"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "🕸️",
        "rarity": RARITY.COMMON,
        ingredients: [
            { itemDef: MATERIALS.Grass, quantity: 1 },
        ]
    },
    "BoneGlue": {
        id: 'BoneGlue',
        get name() { return getText("Bone Glue"); },
        get description() { return getText("desc_bone_glue"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "(images/bone_glue.svg)",
        "rarity": RARITY.COMMON,
        ingredients: [
            { itemDef: MATERIALS.FishBone, quantity: 1 },
        ]
    },
};


export const CRAFTABLE_TOOLS: { [key: string]: CraftableTool} = {
    ...FISHING_RODS,
    "shovel": {
        id: 'shovel',
        get name() { return getText("shovel"); },
        get description() { return getText("desc_shovel"); },
        "type": ResourceTypes.TOOL,
        icon: 'images/tools/shovel.png',
        "rarity": RARITY.COMMON,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: MATERIALS.Log, quantity: 1 },
        ],
        "durability": 100,
        "time": 90,
        weight: 1.5,
    },
    "Stone Knife": {
        id: 'Stone Knife',
        get name() { return getText("Stone Knife"); },
        get description() { return getText("desc_stone_knife"); },
        "type": ResourceTypes.TOOL,
        icon: '🔪',
        "rarity": RARITY.COMMON,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: MATERIALS.Flint, quantity: 1 },
        ],
        "durability": 100,
        "time": 30
    },
    "StoneAxe": {
        id: 'Stone Axe',
        get name() { return getText("Stone Axe"); },
        get description() { return getText("desc_stone_axe"); },
        "type": ResourceTypes.TOOL,
        icon: 'images/tools/stone_axe.png', 
        "rarity": RARITY.COMMON,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: MATERIALS.Flint, quantity: 1 },
            { itemDef: MATERIALS.Log, quantity: 1 },
        ],
        "durability": 100,
        "time": 60,
        weight: 1,
    },
    "Iron Axe": {
        id: 'Iron Axe',
        get name() { return getText("Iron Axe"); },
        get description() { return getText("desc_Iron_Axe"); },
        "type": ResourceTypes.TOOL,
        icon: 'images/tools/iron_axe.png', 
        "durability": 100,
        "rarity": RARITY.UNCOMMON,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ],
    },
    "Steel Axe": {
        id: "Steel Axe",
        get name() { return getText("Steel Axe"); },
        get description() { return getText("desc_Steel_Axe"); },
        "type": ResourceTypes.TOOL,
        "icon": "🪓",
        "durability": 150,
        "rarity": RARITY.RARE,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ],
    },
    "Obsidian Axe": {
        id: "Obsidian Axe",
        get name() { return getText("Obsidian Axe"); },
        get description() { return getText("desc_Obsidian_Axe"); },
        "type": ResourceTypes.TOOL,
        "icon": "🪓",
        "durability": 200,
        "rarity": RARITY.EPIC,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ],
    },
    "Titanium Axe": {
        id: "Titanium Axe",
        get name() { return getText("Titanium Axe"); },
        get description() { return getText("desc_Titanium_Axe"); },
        "type": ResourceTypes.TOOL,
        "icon": "🪓",
        "durability": 250,
        "rarity": RARITY.LEGENDARY,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ],
    },
    "Stone Pickaxe": {
        id: "Stone Pickaxe",
        get name() { return getText("Stone Pickaxe"); },
        get description() { return getText("desc_Stone_Pickaxe"); },
        "type": ResourceTypes.TOOL,
        "icon": "⛏️",
        "durability": 100,
        "rarity": RARITY.COMMON,
        "ingredients": [
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ],
    },
    compost_heap: {
        id: 'compost_heap',
        get name() { return getText("compost_heap"); },
        get description() { return getText("desc_compost_heap"); },
        "type": ResourceTypes.TOOL,
        icon: 'images/farming/compost_heap.png',
        "durability": 100,
        "rarity": RARITY.COMMON,
        "ingredients": [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Grass, quantity: 1 },
        ],
        "time": 30
    },
    sickle: {
        id: 'sickle',
        get name() { return getText("sickle"); },
        get description() { return getText("desc_sickle"); },
        "type": ResourceTypes.TOOL,
        icon: 'images/tools/sickle.png',
        "durability": 100,
        "rarity": RARITY.COMMON,
        "ingredients": [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Grass, quantity: 1 },
        ],
        "time": 30
    }
};

export const CRAFTABLE_TRANSPORTS: { [key: string]: CraftableItem} = {
    sailboat: {
        id: 'sailboat',
        get name() { return getText('boat_sailboat'); },
        get description() { return getText('desc_boat_sailboat'); },
        "type": ResourceTypes.TRANSPORT,
        icon: 'images/transports/sailboat.png',
        durability: 200,
        maxDurability: 200,
        weightCap: 400,
        weight: 100,
        speed: 1.2,
        waterproof: 0.95,
        insulation: 0.9,
        thermalShield: 0.8,
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Grass, quantity: 1 },
        ],
        time: 10,
    },
    tractor: {
        id: 'tractor',
        get name() { return getText("tractor"); },
        get description() { return getText("desc_tractor"); },
        "type": ResourceTypes.TRANSPORT,
        icon: 'images/transports/tractor.png',
        durability: 100,
        maxDurability: 100,
        weightCap: 1000,
        weight: 100,
        speed: 1,
        waterproof: 0,
        insulation: 0,
        thermalShield: 0,
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Grass, quantity: 1 },
        ],
        time: 10,
    }
};

import * as React from 'react';
import { getText } from '../i18n';
import { MacOSModal } from './WindowManagement/MacOSModal';
import { Item } from '../Interfaces';

// Fishing rod selection modal component
export const FishingRodSelectionModal: React.FC<{
  fishingRods: Item[];
  isOpen: boolean;
  onClose: () => void;
  onSelect: (rod: Item) => void;
}> = ({ fishingRods, isOpen, onClose, onSelect }) => {
  return (
    <MacOSModal
      title={getText('Select Fishing Rod')}
      isOpen={isOpen}
      onClose={onClose}
      initialSize={{ width: 450, height: 400 }}
    >
      <div className="fishing-modal">
        {fishingRods.map((rod, index) => (
          <div
            key={index}
            className="fishing-rod-item"
            onClick={() => {
              onSelect(rod);
              onClose();
            }}
          >
            <div className="rod-icon">{rod.icon}</div>
            <div className="rod-info">
              <div className="rod-name">{rod.name}</div>
              <div className="rod-quality">Quality: {rod.rarity || 'common'}</div>
            </div>
          </div>
        ))}
      </div>
    </MacOSModal>
  );
};

// Fishing result modal component
export const FishingResultModal: React.FC<{
  icon: string;
  message: string;
  onClose?: () => void;
}> = ({ icon, message, onClose }) => {
  const [isOpen, setIsOpen] = React.useState(true);

  const handleClose = () => {
    setIsOpen(false);
    if (onClose) onClose();
  };

  return (
    <MacOSModal
      title={getText('Fishing Result')}
      isOpen={isOpen}
      onClose={handleClose}
      initialSize={{ width: 400, height: 300 }}
    >
      <div className="fishing-result-message">
        {icon && <div className="fishing-result-icon">{icon}</div>}
        <p>{message}</p>
        <div className='panel-btn' 
          style={{
            width: "100px"
            // marginTop: '10px',
            // padding: '5px 15px',
            // backgroundColor: '#3498db',
            // color: 'white',
            // border: 'none',
            // borderRadius: '5px',
            // cursor: 'pointer'
          }}
          onClick={handleClose}
        >
          {getText('OK')}
        </div>
      </div>
    </MacOSModal>
  );
};

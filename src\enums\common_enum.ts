// @ts-check

import { getText } from '../i18n.js';
import { ResourceType } from '../Interfaces.js';


/** @import { ResourceType } from './resources.js' */


// export const RARITY = {
//     COMMON: "common",
//     UNCOMMON: "uncommon",
//     RARE: "rare",
//     EPIC: "epic",
//     LEGENDARY: "legendary"
// }

export const RARITY = {
    COMMON: 1,
    UNCOMMON: 2,
    RARE: 3,
    EPIC: 4,
    LEGENDARY: 5
}

export const enum ItemLocation {
  GroundBuilding,      // 0
  Ground,    // 1
  Inventory,    // 2
  InsideABuilding
}

export const ResourceTypes: { [key: string]: ResourceType} = {
    BUILDING: {
        id: 'Building',
        get name() { return getText("resource_type_building"); },
        icon: "images/ui_icons/building.png",
        get description() { return getText("resource_desc_building"); }
    },
    TRANSPORT: {
        id: 'Transport',
        get name() { return getText("resource_type_transport"); },
        icon: "images/ui_icons/boat.png",
        get description() { return getText("resource_desc_transport"); }
    },
    EDIBLE: {
        id: 'Edible',
        get name() { return getText("resource_type_edible"); },
        icon: "🍽️",
        get description() { return getText("resource_desc_edible"); },
        get invActionDesc() { return getText("Eat"); }
    },
    MATERIAL: {
        id: 'Material',
        get name() { return getText("resource_type_material"); },
        icon: "images/ui_icons/sheet.png",
        get description() { return getText("resource_desc_material"); }
    },
    TOOL: {
        id: 'Tool',
        get name() { return getText("resource_type_tool"); },
        icon: "images/ui_icons/tools.svg",
        get description() { return getText("resource_desc_tool"); }
    },
    VALUABLE: {
        id: 'Valuable',
        get name() { return getText("resource_type_valuable"); },
        icon: "💎",
        get description() { return getText("resource_desc_valuable"); }
    },
    MEDICINAL: {
        id: 'Medicinal',
        get name() { return getText("resource_type_medicinal"); },
        icon: "💊",
        get description() { return getText("resource_desc_medicinal"); },
        get invActionDesc() { return getText("Use"); }
    },
    Harvestable: {
        id: 'Harvestable',
        get name() { return getText("resource_type_harvestable"); },
        icon: "🌱",
        get description() { return getText("resource_desc_harvestable"); }
    },
    Equipable: {
        id: 'Equipable',
        get name() { return getText("resource_type_equipable"); },
        icon: "images/ui_icons/backpack.png",
        get description() { return getText("resource_desc_equipable"); },
        get invActionDesc() { return getText("Equip"); }
    },
    HUNTABLE: {
        id: 'Huntable',
        get name() { return getText("resource_type_huntable"); },
        icon: "🐟",
        get description() { return getText("resource_desc_huntable"); }
    },
    UNKNOWN: {
        id: 'Unknown',
        get name() { return getText("resource_type_unknown"); },
        icon: "❓",
        get description() { return getText("resource_desc_unknown"); }
    },
    // TREE: {
    //     id: 'Tree',
    //     get name() { return getText("resource_type_tree"); },
    //     icon: "🌲",
    //     get description() { return getText("resource_desc_tree"); }
    // }
};

export const statsIconMap = {
    food: 'images/ui_icons/stomach.png',
    water: 'images/ui_icons/water.png',
    energy: 'images/ui_icons/lightning.svg',
    health: 'images/ui_icons/heart.svg', 
}
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { InventoryItemStack } from 'src/Interfaces';
import { getText } from 'src/i18n';
import { useRootStore } from 'src/stores/rootStore';
import { useGroundStore } from 'src/stores/groundStore';
import { Items } from 'src/enums/resources';
import * as PIXI from 'pixi.js';
import { GlowFilter } from '@pixi/filter-glow';
import { Renderer } from '@pixi/core';


// Texture cache for item icons
const textureCache = new Map<string, PIXI.Texture>();

// Load texture with fallback
const loadItemTexture = async (iconPath: string): Promise<PIXI.Texture> => {
  if (textureCache.has(iconPath)) {
    return textureCache.get(iconPath)!;
  }

  try {
    const texture = await PIXI.Assets.load(iconPath);
    // const texture = await PIXI.Texture.fromURL(iconPath);
    textureCache.set(iconPath, texture);
    return texture;
  } catch (error) {
    // Create fallback texture
    const graphics = new PIXI.Graphics();
    graphics.beginFill(0x666666);
    graphics.drawRoundedRect(0, 0, 32, 32, 4);
    graphics.endFill();

    const fallbackTexture = PIXI.RenderTexture.create({ width: 32, height: 32 });
    const renderer = new Renderer({ width: 800, height: 600 });
    renderer.render(graphics, { renderTexture: fallbackTexture });

    textureCache.set(iconPath, fallbackTexture);
    return fallbackTexture;
  }
};

// PixiJS-based GroundGrid component for better performance
export const PixiGroundGrid = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const appRef = useRef<PIXI.Application | null>(null);
  const [selectedInventoryStack, setSelectedInventoryStack] = useState<InventoryItemStack | null>(null);
  const [selectedGroundStorageStack, setSelectedGroundStorageStack] = useState<InventoryItemStack | null>(null);
  const [draggedItem, setDraggedItem] = useState<{
    item: InventoryItemStack;
    sourceType: 'inventory' | 'storage';
    sourceIndex: number;
    dragSprite?: PIXI.Container;
  } | null>(null);

  // Drag state
  const dragStateRef = useRef<{
    isDragging: boolean;
    startPos: { x: number; y: number };
    dragSprite: PIXI.Container | null;
    sourceSlot: PIXI.Container | null;
  }>({
    isDragging: false,
    startPos: { x: 0, y: 0 },
    dragSprite: null,
    sourceSlot: null,
  });

  // Get inventory and storage data from stores
  const itemStacks = useRootStore(state => state.itemStacks);
  const setItemStacks = useRootStore(state => state.setItemStacks);
  const currRegionIndex = useRootStore(state => state.currRegionIndex);
  const groundStorageStacks = useGroundStore(state => state.allRegionGroundStacks[currRegionIndex]);
  const addStackByIndex = useGroundStore(state => state.addStackByIndex);
  const removeGroundStackByIndex = useGroundStore(state => state.removeStackByIndex);
  const swapGroundStorageStacks = useGroundStore(state => state.swapStacks);
  const swapInventoryStacks = useRootStore(state => state.swapStacks);

  // Responsive grid configuration
  const [screenSize, setScreenSize] = useState({ width: window.innerWidth, height: window.innerHeight });

  const getGridConfig = () => {
    const isMobile = screenSize.width <= 768;
    const isTablet = screenSize.width <= 1024 && screenSize.width > 768;

    return {
      CELL_SIZE: isMobile ? 35 : isTablet ? 40 : 45,
      CELL_PADDING: isMobile ? 1 : 2,
      INVENTORY_COLS: isMobile ? 8 : 10,
      STORAGE_COLS: isMobile ? 8 : 10,
      INVENTORY_ROWS: Math.ceil(itemStacks.length / (isMobile ? 8 : 10)) + 1,
      STORAGE_ROWS: Math.ceil((groundStorageStacks?.length || 0) / (isMobile ? 8 : 10)) + 1,
      TITLE_HEIGHT: isMobile ? 30 : 40,
      SECTION_SPACING: isMobile ? 15 : 20,
    };
  };

  const GRID_CONFIG = getGridConfig();

  // Track screen size changes
  useEffect(() => {
    const handleResize = () => {
      setScreenSize({ width: window.innerWidth, height: window.innerHeight });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Initialize PixiJS application with responsive sizing
  useEffect(() => {
    if (!canvasRef.current || appRef.current) return;

    const parent = canvasRef.current.parentElement;
    const width = parent?.clientWidth || 800;
    const height = parent?.clientHeight || 600;

    const app = new PIXI.Application({
      view: canvasRef.current,
      width,
      height,
      backgroundColor: 0x1a1a1a,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
      autoDensity: true,
    });

    appRef.current = app;

    // Create main containers with responsive positioning
    const inventoryContainer = new PIXI.Container();
    inventoryContainer.name = 'inventory';
    inventoryContainer.x = 10;
    inventoryContainer.y = GRID_CONFIG.TITLE_HEIGHT + 10;

    const storageContainer = new PIXI.Container();
    storageContainer.name = 'storage';
    storageContainer.x = 10;

    // Calculate storage position based on inventory size
    const inventoryHeight = GRID_CONFIG.INVENTORY_ROWS * (GRID_CONFIG.CELL_SIZE + GRID_CONFIG.CELL_PADDING);
    storageContainer.y = inventoryContainer.y + inventoryHeight + GRID_CONFIG.SECTION_SPACING + GRID_CONFIG.TITLE_HEIGHT;

    app.stage.addChild(inventoryContainer);
    app.stage.addChild(storageContainer);

    // Add titles with responsive font sizes
    const titleFontSize = screenSize.width <= 768 ? 14 : 16;

    const inventoryTitle = new PIXI.Text(getText('Inventory'), {
      fontFamily: 'Arial',
      fontSize: titleFontSize,
      fill: 0xe0e0e0,
      fontWeight: 'bold',
    });
    inventoryTitle.x = 10;
    inventoryTitle.y = 10;
    app.stage.addChild(inventoryTitle);

    const storageTitle = new PIXI.Text(getText('Storage'), {
      fontFamily: 'Arial',
      fontSize: titleFontSize,
      fill: 0xe0e0e0,
      fontWeight: 'bold',
    });
    storageTitle.x = 10;
    storageTitle.y = storageContainer.y - GRID_CONFIG.TITLE_HEIGHT;
    app.stage.addChild(storageTitle);

    return () => {
      if (appRef.current) {
        appRef.current.destroy(true);
        appRef.current = null;
      }
    };
  }, []);

  // Performance optimization: Cache slot containers
  const slotCache = useRef<Map<string, PIXI.Container>>(new Map());

  // Render inventory grid with performance optimizations
  const renderInventoryGrid = useCallback(() => {
    if (!appRef.current) return;

    const inventoryContainer = appRef.current.stage.getChildByName('inventory') as PIXI.Container;
    if (!inventoryContainer) return;

    // Performance: Only update changed slots instead of recreating all
    const totalSlots = Math.max(itemStacks.length + 5, 20);
    const currentSlots = inventoryContainer.children.length;

    // Add new slots if needed
    for (let i = currentSlots; i < totalSlots; i++) {
      const col = i % GRID_CONFIG.INVENTORY_COLS;
      const row = Math.floor(i / GRID_CONFIG.INVENTORY_COLS);

      const slot = createItemSlot(
        col * (GRID_CONFIG.CELL_SIZE + GRID_CONFIG.CELL_PADDING),
        row * (GRID_CONFIG.CELL_SIZE + GRID_CONFIG.CELL_PADDING),
        itemStacks[i],
        'inventory',
        i
      );

      inventoryContainer.addChild(slot);
    }

    // Update existing slots with new data
    for (let i = 0; i < Math.min(currentSlots, totalSlots); i++) {
      const slotContainer = inventoryContainer.children[i] as PIXI.Container;
      const itemStack = itemStacks[i];

      // Update slot data
      (slotContainer as any).slotData = {
        type: 'inventory',
        index: i,
        itemStack,
      };

      // Update visual representation if item changed
      updateSlotVisuals(slotContainer, itemStack);
    }

    // Remove excess slots if inventory shrunk
    while (inventoryContainer.children.length > totalSlots) {
      const lastChild = inventoryContainer.children[inventoryContainer.children.length - 1];
      inventoryContainer.removeChild(lastChild);
    }
  }, [itemStacks, GRID_CONFIG]);

  // Update slot visuals without recreating the entire slot
  const updateSlotVisuals = (slotContainer: PIXI.Container, itemStack: InventoryItemStack | null) => {
    // Remove old item visuals (keep background and selection effects)
    const itemChildren = slotContainer.children.slice(2); // Skip background and inner shadow
    itemChildren.forEach(child => slotContainer.removeChild(child));

    if (itemStack) {
      const itemDef = Items[itemStack.itemId];
      if (itemDef) {
        // Add new item visuals
        const itemContainer = new PIXI.Container();

        // Simplified item icon for performance
        const itemIcon = new PIXI.Graphics();
        const color = getItemColor(itemDef);
        itemIcon.beginFill(color);
        itemIcon.drawRoundedRect(5, 5, 35, 35, 2);
        itemIcon.endFill();
        itemContainer.addChild(itemIcon);

        slotContainer.addChild(itemContainer);

        // Add quantity if needed
        if (itemStack.quantity > 1) {
          const quantityBg = new PIXI.Graphics();
          quantityBg.beginFill(0x000000, 0.8);
          quantityBg.drawCircle(0, 0, 10);
          quantityBg.endFill();
          quantityBg.x = GRID_CONFIG.CELL_SIZE - 12;
          quantityBg.y = GRID_CONFIG.CELL_SIZE - 12;

          const quantityText = new PIXI.Text(itemStack.quantity.toString(), {
            fontFamily: 'Arial',
            fontSize: 10,
            fill: 0xffffff,
            fontWeight: 'bold',
          });
          quantityText.anchor.set(0.5);
          quantityText.x = GRID_CONFIG.CELL_SIZE - 12;
          quantityText.y = GRID_CONFIG.CELL_SIZE - 12;

          slotContainer.addChild(quantityBg);
          slotContainer.addChild(quantityText);
        }
      }
    }
  };

  // Render storage grid with performance optimizations
  const renderStorageGrid = useCallback(() => {
    if (!appRef.current || !groundStorageStacks) return;

    const storageContainer = appRef.current.stage.getChildByName('storage') as PIXI.Container;
    if (!storageContainer) return;

    // Performance: Only update changed slots
    const totalSlots = Math.max(groundStorageStacks.length + 5, 20);
    const currentSlots = storageContainer.children.length;

    // Add new slots if needed
    for (let i = currentSlots; i < totalSlots; i++) {
      const col = i % GRID_CONFIG.STORAGE_COLS;
      const row = Math.floor(i / GRID_CONFIG.STORAGE_COLS);

      const slot = createItemSlot(
        col * (GRID_CONFIG.CELL_SIZE + GRID_CONFIG.CELL_PADDING),
        row * (GRID_CONFIG.CELL_SIZE + GRID_CONFIG.CELL_PADDING),
        groundStorageStacks[i],
        'storage',
        i
      );

      storageContainer.addChild(slot);
    }

    // Update existing slots
    for (let i = 0; i < Math.min(currentSlots, totalSlots); i++) {
      const slotContainer = storageContainer.children[i] as PIXI.Container;
      const itemStack = groundStorageStacks[i];

      (slotContainer as any).slotData = {
        type: 'storage',
        index: i,
        itemStack,
      };

      updateSlotVisuals(slotContainer, itemStack);
    }

    // Remove excess slots
    while (storageContainer.children.length > totalSlots) {
      const lastChild = storageContainer.children[storageContainer.children.length - 1];
      storageContainer.removeChild(lastChild);
    }
  }, [groundStorageStacks, GRID_CONFIG]);

  // Create individual item slot with enhanced styling
  const createItemSlot = (
    x: number,
    y: number,
    itemStack: InventoryItemStack | null,
    type: 'inventory' | 'storage',
    index: number
  ): PIXI.Container => {
    const slotContainer = new PIXI.Container();
    slotContainer.x = x;
    slotContainer.y = y;

    // Create slot background with metallic styling
    const slotBg = new PIXI.Graphics();

    // Base background with gradient effect
    slotBg.beginFill(0x1a1a1a);
    slotBg.lineStyle(1, 0x3a3a3a);
    slotBg.drawRoundedRect(0, 0, GRID_CONFIG.CELL_SIZE, GRID_CONFIG.CELL_SIZE, 2);
    slotBg.endFill();

    // Add inner shadow effect
    const innerShadow = new PIXI.Graphics();
    innerShadow.beginFill(0x000000, 0.3);
    innerShadow.drawRoundedRect(1, 1, GRID_CONFIG.CELL_SIZE - 2, GRID_CONFIG.CELL_SIZE - 2, 1);
    innerShadow.endFill();

    slotContainer.addChild(slotBg);
    slotContainer.addChild(innerShadow);

    // Add item if present
    if (itemStack) {
      const itemDef = Items[itemStack.itemId];
      if (itemDef) {
        // Create item icon container
        const itemContainer = new PIXI.Container();

        // Create item sprite with proper texture loading
        const createItemSprite = async () => {
          let itemSprite: PIXI.Sprite;

          if (itemDef.icon && itemDef.icon !== '') {
            try {
              const texture = await loadItemTexture(itemDef.icon);
              itemSprite = new PIXI.Sprite(texture);
            } catch {
              // Fallback to colored rectangle
              const graphics = new PIXI.Graphics();
              const color = getItemColor(itemDef);
              graphics.beginFill(color);
              graphics.drawRoundedRect(0, 0, 32, 32, 2);
              graphics.endFill();

              const fallbackTexture = PIXI.RenderTexture.create({ width: 32, height: 32 });
              if (appRef.current) {
                appRef.current.renderer.render(graphics, { renderTexture: fallbackTexture });
              }
              itemSprite = new PIXI.Sprite(fallbackTexture);
            }
          } else {
            // Create fallback sprite
            const graphics = new PIXI.Graphics();
            const color = getItemColor(itemDef);
            graphics.beginFill(color);
            graphics.drawRoundedRect(0, 0, 32, 32, 2);
            graphics.endFill();

            const fallbackTexture = PIXI.RenderTexture.create({ width: 32, height: 32 });
            if (appRef.current) {
              appRef.current.renderer.render(graphics, { renderTexture: fallbackTexture });
            }
            itemSprite = new PIXI.Sprite(fallbackTexture);
          }

          // Position and scale the sprite
          itemSprite.x = 6;
          itemSprite.y = 6;
          itemSprite.width = 33;
          itemSprite.height = 33;

          // Add drop shadow effect
          const dropShadow = new PIXI.Graphics();
          dropShadow.beginFill(0x000000, 0.3);
          dropShadow.drawRoundedRect(8, 8, 33, 33, 2);
          dropShadow.endFill();
          itemContainer.addChild(dropShadow);

          itemContainer.addChild(itemSprite);

          // Add rarity border effect
          if (itemDef.rarity && itemDef.rarity > 1) {
            const rarityBorder = new PIXI.Graphics();
            const rarityColor = getRarityColor(itemDef.rarity);
            rarityBorder.lineStyle(2, rarityColor, 0.8);
            rarityBorder.drawRoundedRect(4, 4, 37, 37, 3);

            // Add subtle glow effect for higher rarities
            if (itemDef.rarity >= 4) {
              const glowFilter = new GlowFilter({
                color: rarityColor,
                distance: 5,
                outerStrength: 1,
                innerStrength: 0.5,
              });
              rarityBorder.filters = [glowFilter];
            }

            itemContainer.addChild(rarityBorder);
          }
        };

        // Load sprite asynchronously
        createItemSprite();

        slotContainer.addChild(itemContainer);

        // Add quantity text if > 1
        if (itemStack.quantity > 1) {
          const quantityBg = new PIXI.Graphics();
          quantityBg.beginFill(0x000000, 0.8);
          quantityBg.drawCircle(0, 0, 10);
          quantityBg.endFill();
          quantityBg.x = GRID_CONFIG.CELL_SIZE - 12;
          quantityBg.y = GRID_CONFIG.CELL_SIZE - 12;

          const quantityText = new PIXI.Text(itemStack.quantity.toString(), {
            fontFamily: 'Arial',
            fontSize: 10,
            fill: 0xffffff,
            fontWeight: 'bold',
          });
          quantityText.anchor.set(0.5);
          quantityText.x = GRID_CONFIG.CELL_SIZE - 12;
          quantityText.y = GRID_CONFIG.CELL_SIZE - 12;

          slotContainer.addChild(quantityBg);
          slotContainer.addChild(quantityText);
        }

        // Add freshness indicator if applicable
        if (itemStack.freshness !== undefined && itemDef.freshness) {
          const freshnessBar = new PIXI.Graphics();
          const freshnessRatio = itemStack.freshness / itemDef.freshness;
          const barColor = freshnessRatio > 0.5 ? 0x2ecc71 : freshnessRatio > 0.2 ? 0xf39c12 : 0xe74c3c;

          freshnessBar.beginFill(barColor, 0.3);
          freshnessBar.drawRect(2, GRID_CONFIG.CELL_SIZE - 6, (GRID_CONFIG.CELL_SIZE - 4) * freshnessRatio, 4);
          freshnessBar.endFill();
          slotContainer.addChild(freshnessBar);
        }
      }
    }

    // Store metadata for interactions
    (slotContainer as any).slotData = {
      type,
      index,
      itemStack,
    };

    // Make slot interactive
    slotContainer.interactive = true;
    slotContainer.buttonMode = true;

    // Add selection highlighting
    const updateSelectionState = () => {
      const isSelected = (type === 'inventory' && selectedInventoryStack?.uuid === itemStack?.uuid) ||
                        (type === 'storage' && selectedGroundStorageStack?.uuid === itemStack?.uuid);

      if (isSelected) {
        slotBg.tint = 0x4a90e2;
        slotBg.alpha = 0.8;

        // Add selection glow effect
        const glowFilter = new PIXI.filters.GlowFilter({
          color: 0x4a90e2,
          distance: 8,
          outerStrength: 2,
          innerStrength: 1,
        });
        slotContainer.filters = [glowFilter];
      } else {
        slotBg.tint = 0xffffff;
        slotBg.alpha = 1;
        slotContainer.filters = [];
      }
    };

    // Initial selection state
    updateSelectionState();

    // Add hover effects with smooth animations
    slotContainer.on('pointerover', () => {
      if (!dragStateRef.current.isDragging) {
        // Animate hover effect
        const hoverTween = {
          scale: slotContainer.scale.x,
          tint: slotBg.tint,
        };

        // Simple animation using requestAnimationFrame
        const animateHover = () => {
          hoverTween.scale = Math.min(hoverTween.scale + 0.01, 1.05);
          hoverTween.tint = 0xdddddd;

          slotContainer.scale.set(hoverTween.scale);
          slotBg.tint = hoverTween.tint;

          if (hoverTween.scale < 1.05) {
            requestAnimationFrame(animateHover);
          }
        };

        if (itemStack) {
          animateHover();
        } else {
          slotBg.tint = 0xdddddd;
        }
      }
    });

    slotContainer.on('pointerout', () => {
      if (!dragStateRef.current.isDragging) {
        // Animate back to normal
        const normalTween = {
          scale: slotContainer.scale.x,
        };

        const animateNormal = () => {
          normalTween.scale = Math.max(normalTween.scale - 0.01, 1.0);
          slotContainer.scale.set(normalTween.scale);

          if (normalTween.scale > 1.0) {
            requestAnimationFrame(animateNormal);
          }
        };

        animateNormal();
        updateSelectionState(); // Restore selection state
      }
    });

    // Add drag and drop handlers
    slotContainer.on('pointerdown', (event) => {
      if (itemStack) {
        // Start drag operation
        const globalPos = event.data.global;
        dragStateRef.current = {
          isDragging: true,
          startPos: { x: globalPos.x, y: globalPos.y },
          dragSprite: null,
          sourceSlot: slotContainer,
        };

        setDraggedItem({
          item: itemStack,
          sourceType: type,
          sourceIndex: index,
        });

        // Create drag sprite
        createDragSprite(itemStack, globalPos.x, globalPos.y);

        // Set selection
        if (type === 'inventory') {
          setSelectedInventoryStack(itemStack);
        } else {
          setSelectedGroundStorageStack(itemStack);
        }
      }
    });

    // Handle drag over for drop zones
    slotContainer.on('pointerover', () => {
      if (dragStateRef.current.isDragging && draggedItem) {
        // Highlight as valid drop zone
        slotBg.tint = 0x4a90e2;
      }
    });

    slotContainer.on('pointerout', () => {
      if (!dragStateRef.current.isDragging) {
        slotBg.tint = 0xffffff;
        slotContainer.scale.set(1.0);
      } else if (draggedItem) {
        // Remove drop zone highlight
        slotBg.tint = 0xffffff;
      }
    });

    // Handle drop
    slotContainer.on('pointerup', () => {
      if (dragStateRef.current.isDragging && draggedItem) {
        handleDrop(type, index);
      }
    });

    return slotContainer;
  };

  // Helper function to get item color based on type
  const getItemColor = (itemDef: any): number => {
    const typeColors: { [key: string]: number } = {
      'food': 0x8bc34a,
      'tool': 0x795548,
      'weapon': 0xf44336,
      'material': 0x9e9e9e,
      'consumable': 0x2196f3,
    };
    return typeColors[itemDef.type?.id] || 0x666666;
  };

  // Helper function to get rarity color
  const getRarityColor = (rarity: number): number => {
    const rarityColors = [
      0x9e9e9e, // Common (gray)
      0x4caf50, // Uncommon (green)
      0x2196f3, // Rare (blue)
      0x9c27b0, // Epic (purple)
      0xffc107, // Legendary (gold)
    ];
    return rarityColors[Math.min(rarity - 1, rarityColors.length - 1)] || 0x9e9e9e;
  };

  // Create drag sprite
  const createDragSprite = (itemStack: InventoryItemStack, x: number, y: number) => {
    if (!appRef.current) return;

    const itemDef = Items[itemStack.itemId];
    if (!itemDef) return;

    // Create drag container
    const dragContainer = new PIXI.Container();
    dragContainer.x = x;
    dragContainer.y = y;
    dragContainer.alpha = 0.8;
    dragContainer.scale.set(1.1); // Slightly larger when dragging

    // Create drag background
    const dragBg = new PIXI.Graphics();
    dragBg.beginFill(0x4a90e2, 0.8);
    dragBg.lineStyle(2, 0xffffff, 0.8);
    dragBg.drawRoundedRect(-22, -22, 44, 44, 4);
    dragBg.endFill();
    dragContainer.addChild(dragBg);

    // Add item icon (simplified version)
    const itemIcon = new PIXI.Graphics();
    const color = getItemColor(itemDef);
    itemIcon.beginFill(color);
    itemIcon.drawRoundedRect(-16, -16, 32, 32, 2);
    itemIcon.endFill();
    dragContainer.addChild(itemIcon);

    // Add quantity if > 1
    if (itemStack.quantity > 1) {
      const quantityText = new PIXI.Text(itemStack.quantity.toString(), {
        fontFamily: 'Arial',
        fontSize: 12,
        fill: 0xffffff,
        fontWeight: 'bold',
      });
      quantityText.anchor.set(0.5);
      quantityText.x = 15;
      quantityText.y = 15;
      dragContainer.addChild(quantityText);
    }

    // Add to stage
    appRef.current.stage.addChild(dragContainer);
    dragStateRef.current.dragSprite = dragContainer;

    // Set up mouse move handler
    const onMouseMove = (event: any) => {
      if (dragContainer && dragStateRef.current.isDragging) {
        const globalPos = event.data.global;
        dragContainer.x = globalPos.x;
        dragContainer.y = globalPos.y;
      }
    };

    appRef.current.stage.interactive = true;
    appRef.current.stage.on('pointermove', onMouseMove);

    // Clean up on mouse up
    const onMouseUp = () => {
      if (dragContainer) {
        appRef.current?.stage.removeChild(dragContainer);
        appRef.current?.stage.off('pointermove', onMouseMove);
        appRef.current?.stage.off('pointerup', onMouseUp);
      }
      dragStateRef.current = {
        isDragging: false,
        startPos: { x: 0, y: 0 },
        dragSprite: null,
        sourceSlot: null,
      };
      setDraggedItem(null);
    };

    appRef.current.stage.on('pointerup', onMouseUp);
  };

  // Create transfer animation effect
  const createTransferEffect = (fromX: number, fromY: number, toX: number, toY: number) => {
    if (!appRef.current) return;

    // Create particle effect
    const particles: PIXI.Graphics[] = [];
    const particleCount = 8;

    for (let i = 0; i < particleCount; i++) {
      const particle = new PIXI.Graphics();
      particle.beginFill(0x4a90e2, 0.8);
      particle.drawCircle(0, 0, 2);
      particle.endFill();

      particle.x = fromX;
      particle.y = fromY;

      appRef.current.stage.addChild(particle);
      particles.push(particle);
    }

    // Animate particles
    let progress = 0;
    const animateParticles = () => {
      progress += 0.05;

      particles.forEach((particle, index) => {
        const angle = (index / particleCount) * Math.PI * 2;
        const spread = Math.sin(progress * Math.PI) * 20;

        particle.x = fromX + (toX - fromX) * progress + Math.cos(angle) * spread;
        particle.y = fromY + (toY - fromY) * progress + Math.sin(angle) * spread;
        particle.alpha = 1 - progress;
      });

      if (progress < 1) {
        requestAnimationFrame(animateParticles);
      } else {
        // Clean up particles
        particles.forEach(particle => {
          appRef.current?.stage.removeChild(particle);
        });
      }
    };

    animateParticles();
  };

  // Handle drop operation with visual effects
  const handleDrop = (targetType: 'inventory' | 'storage', targetIndex: number) => {
    if (!draggedItem) return;

    const { sourceType, sourceIndex, item } = draggedItem;

    // Prevent dropping on the same slot
    if (sourceType === targetType && sourceIndex === targetIndex) {
      return;
    }

    // Calculate positions for transfer effect
    const sourceContainer = sourceType === 'inventory' ?
      appRef.current?.stage.getChildByName('inventory') :
      appRef.current?.stage.getChildByName('storage');
    const targetContainer = targetType === 'inventory' ?
      appRef.current?.stage.getChildByName('inventory') :
      appRef.current?.stage.getChildByName('storage');

    if (sourceContainer && targetContainer) {
      const sourceCols = sourceType === 'inventory' ? GRID_CONFIG.INVENTORY_COLS : GRID_CONFIG.STORAGE_COLS;
      const targetCols = targetType === 'inventory' ? GRID_CONFIG.INVENTORY_COLS : GRID_CONFIG.STORAGE_COLS;

      const sourceCol = sourceIndex % sourceCols;
      const sourceRow = Math.floor(sourceIndex / sourceCols);
      const targetCol = targetIndex % targetCols;
      const targetRow = Math.floor(targetIndex / targetCols);

      const fromX = sourceContainer.x + sourceCol * (GRID_CONFIG.CELL_SIZE + GRID_CONFIG.CELL_PADDING) + GRID_CONFIG.CELL_SIZE / 2;
      const fromY = sourceContainer.y + sourceRow * (GRID_CONFIG.CELL_SIZE + GRID_CONFIG.CELL_PADDING) + GRID_CONFIG.CELL_SIZE / 2;
      const toX = targetContainer.x + targetCol * (GRID_CONFIG.CELL_SIZE + GRID_CONFIG.CELL_PADDING) + GRID_CONFIG.CELL_SIZE / 2;
      const toY = targetContainer.y + targetRow * (GRID_CONFIG.CELL_SIZE + GRID_CONFIG.CELL_PADDING) + GRID_CONFIG.CELL_SIZE / 2;

      createTransferEffect(fromX, fromY, toX, toY);
    }

    // Handle different drop scenarios
    if (sourceType === 'inventory' && targetType === 'storage') {
      // Move from inventory to storage
      const newItemStacks = [...itemStacks];
      newItemStacks[sourceIndex] = null;
      setItemStacks(newItemStacks);
      addStackByIndex(currRegionIndex, targetIndex, item);
    } else if (sourceType === 'storage' && targetType === 'inventory') {
      // Move from storage to inventory
      const newItemStacks = [...itemStacks];
      newItemStacks[targetIndex] = item;
      setItemStacks(newItemStacks);
      removeGroundStackByIndex(currRegionIndex, sourceIndex);
    } else if (sourceType === 'storage' && targetType === 'storage') {
      // Swap within storage
      swapGroundStorageStacks(currRegionIndex, sourceIndex, targetIndex);
    } else if (sourceType === 'inventory' && targetType === 'inventory') {
      // Swap within inventory
      swapInventoryStacks(sourceIndex, targetIndex);
    }
  };

  // Update grids when data changes with error handling
  useEffect(() => {
    try {
      renderInventoryGrid();
    } catch (error) {
      console.error('Error rendering inventory grid:', error);
    }
  }, [renderInventoryGrid, itemStacks]);

  useEffect(() => {
    try {
      renderStorageGrid();
    } catch (error) {
      console.error('Error rendering storage grid:', error);
    }
  }, [renderStorageGrid, groundStorageStacks]);

  // Update selection highlighting when selection state changes
  useEffect(() => {
    if (!appRef.current) return;

    // Update all inventory slots
    const inventoryContainer = appRef.current.stage.getChildByName('inventory') as PIXI.Container;
    if (inventoryContainer) {
      inventoryContainer.children.forEach((child, index) => {
        const slotContainer = child as PIXI.Container;
        const slotBg = slotContainer.children[0] as PIXI.Graphics;
        const itemStack = itemStacks[index];

        if (itemStack && selectedInventoryStack?.uuid === itemStack.uuid) {
          slotBg.tint = 0x4a90e2;
          slotBg.alpha = 0.8;

          const glowFilter = new PIXI.filters.GlowFilter({
            color: 0x4a90e2,
            distance: 8,
            outerStrength: 2,
            innerStrength: 1,
          });
          slotContainer.filters = [glowFilter];
        } else {
          slotBg.tint = 0xffffff;
          slotBg.alpha = 1;
          slotContainer.filters = [];
        }
      });
    }
  }, [selectedInventoryStack, itemStacks]);

  useEffect(() => {
    if (!appRef.current || !groundStorageStacks) return;

    // Update all storage slots
    const storageContainer = appRef.current.stage.getChildByName('storage') as PIXI.Container;
    if (storageContainer) {
      storageContainer.children.forEach((child, index) => {
        const slotContainer = child as PIXI.Container;
        const slotBg = slotContainer.children[0] as PIXI.Graphics;
        const itemStack = groundStorageStacks[index];

        if (itemStack && selectedGroundStorageStack?.uuid === itemStack.uuid) {
          slotBg.tint = 0x4a90e2;
          slotBg.alpha = 0.8;

          const glowFilter = new PIXI.filters.GlowFilter({
            color: 0x4a90e2,
            distance: 8,
            outerStrength: 2,
            innerStrength: 1,
          });
          slotContainer.filters = [glowFilter];
        } else {
          slotBg.tint = 0xffffff;
          slotBg.alpha = 1;
          slotContainer.filters = [];
        }
      });
    }
  }, [selectedGroundStorageStack, groundStorageStacks]);

  // Handle canvas resize
  useEffect(() => {
    const handleResize = () => {
      if (appRef.current && canvasRef.current) {
        const parent = canvasRef.current.parentElement;
        if (parent) {
          appRef.current.renderer.resize(parent.clientWidth, parent.clientHeight);
        }
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initial resize

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  if (!itemStacks || !groundStorageStacks) {
    return null;
  }

  return (
    <div className="pixi-ground-grid storage-modal" style={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      gap: '20px',
      padding: '10px',
      background: 'rgba(0, 0, 0, 0.1)', // Subtle background to match original
    }}>
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          borderRadius: '4px',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        }}
      />
    </div>
  );
};

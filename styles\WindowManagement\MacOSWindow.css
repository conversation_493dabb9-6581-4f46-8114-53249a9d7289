.macos-window {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 1px;
  /* box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15); */
  min-width: 300px;
  min-height: 200px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.2s ease;
  pointer-events: auto; /* Ensure windows capture pointer events */
}

/* Dark mode styles */
.macos-window.dark-mode {
  background-color: rgba(40, 40, 40, 0.95);
  border: 2px solid rgba(0, 0, 0, 0.3);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  /* box-shadow: 0 0 2px rgba(255, 255, 255, 0.2); */
}

.macos-window:hover {
  box-shadow: 0 0px 2px rgb(255 255 255 / 20%);
}

.dark-mode:hover {
  /* box-shadow: 0 12px 28px rgba(0, 0, 0, 0.4); */
}

.window-titlebar {
  /* color: var(--color-gray); */
  height: 38px;
  /* background: linear-gradient(to bottom, #f9f9f9, #e8e8e8); */
  background-color: rgb(0 0 0 / 87%);
  border-bottom: 2px solid #3a3a3a;
  display: flex;
  align-items: center;
  padding: 0 11px;
  user-select: none;
  /* border-top-left-radius: 8px; */
  /* border-top-right-radius: 8px; */
}

/* Dark mode titlebar */
  /* background: linear-gradient(to bottom, #3a3a3a, #2a2a2a); */
/* .dark-mode .window-titlebar {
  background: linear-gradient(to bottom, #1b1b1b, #181818);
  border-bottom: 1px solid #222;
} */

.window-controls {
  display: flex;
  gap: 8px;
  margin-right: 8px;
}

.window-control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  transition: all 0.2s ease;
  position: relative;
}

.window-control.close {
  background-color: #ff5f57;
}

.window-control.minimize {
  background-color: #ffbd2e;
}

.window-control.maximize {
  background-color: #28c940;
}

/* Add symbols to window controls on hover */
.dark-mode .window-control.close:hover::before {
  content: "×";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
  color: rgba(0, 0, 0, 0.5);
}

.dark-mode .window-control.minimize:hover::before {
  content: "−";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
  color: rgba(0, 0, 0, 0.5);
}

.dark-mode .window-control.maximize:hover::before {
  content: "+";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
  color: rgba(0, 0, 0, 0.5);
}

.window-control:hover {
  filter: brightness(0.9);
}

.window-title {
  flex: 1;
  text-align: center;
  font-size: 13px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Dark mode title */
.dark-mode .window-title {
  color: #b9b9b9;
}

.window-titlebar-spacer {
  display: flex;
  /* width: 16px; */ /* Match the width of the controls for centering */
}

.window-content {
  /* display: flex; */
  flex: 1;
  overflow: auto;
  padding: 16px;
  background-color: rgba(255, 255, 255, 0.95);
  height: calc(100% - 38px); /* Subtract titlebar height */
  box-sizing: border-box;
}

/* Dark mode content */
.dark-mode .window-content {
  background-color: rgb(0 0 0 / 87%);
  color: #e0e0e0;
}

.window-content-inner {
  background: rgba(0, 0, 0, 0.92);
  padding: 10px;
}

/* Dock animation for minimized windows */
@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.macos-window.minimized {
  animation: bounce 0.5s ease;
}

/* Resize handles */
.resize-handle {
  position: absolute;
  background-color: transparent;
  z-index: 10;
}

.resize-handle-right {
  width: 8px;
  height: calc(100% - 38px); /* Subtract titlebar height */
  right: 0;
  bottom: 0;
  cursor: e-resize;
  cursor: url('../cursor/resize_a_horizontal.svg') 9 9, e-resize;
}

.resize-handle-bottom {
  width: calc(100% - 8px); /* Subtract corner width */
  height: 8px;
  left: 0;
  bottom: 0;
  /* cursor: s-resize; */
  cursor: url('../cursor/resize_a_vertical.svg') 9 9, s-resize;
}

.resize-handle-corner {
  width: 16px;
  height: 16px;
  right: 0;
  bottom: 0;
  /* cursor: se-resize; */
  cursor: url('../cursor/resize_se.svg') 9 9, se-resize;
}

/* Show resize handles on hover */
.macos-window:hover .resize-handle-right,
.macos-window:hover .resize-handle-bottom,
.macos-window:hover .resize-handle-corner {
  opacity: 0.5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .macos-window {
    min-width: 250px;
  }
}

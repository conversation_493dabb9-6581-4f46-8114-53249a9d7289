import React from 'react';
import { getText } from '../i18n';
import { MacOSModal } from './WindowManagement/MacOSModal';
import { CraftableItem } from '../Interfaces';
import { Items } from '../enums/resources';
import { ItemIcon } from './common';
import { useRootStore } from 'src/stores/rootStore';
import { ResourceTypes } from 'src/enums/common_enum';
import { useGroundStore } from 'src/stores/groundStore';
import { useLocationStore } from 'src/stores/locationStore';
import { v4 as uuidv4 } from 'uuid';

interface RecipeDetailsModalProps {
  recipe: CraftableItem;
  isOpen: boolean;
  onClose: () => void;
  canCraft: boolean;
  getIngredientQuantity: (ingredientId: string) => number;
  onCraft: () => void;
  selectedRecipe: CraftableItem;
}

export const RecipeDetailsModal: React.FC<RecipeDetailsModalProps> = ({
  recipe,
  isOpen,
  onClose,
  canCraft,
  getIngredientQuantity,
  onCraft,
  selectedRecipe
}) => {
  if (!recipe) return null;

  const currRegionIndex = useRootStore(state => state.currRegionIndex);
  const startProgressBar = useRootStore(state => state.startProgressBar);
  const removeItemsFromInventory = useRootStore(state => state.removeItemsFromInventory);
  const addItemToInventory = useRootStore(state => state.addItemToInventory);
  const addToGroundBuildings = useGroundStore(state => state.addToGroundBuildings);

  const currentLocationBuildingUuid = useLocationStore(state => state.currentLocationBuildingUuid);
  const addStackToABuilding = useGroundStore(state => state.addStackToABuilding);

  const handleCraft = () => {
      if (!selectedRecipe || !canCraft) return;

      startProgressBar(getText('Crafting'), selectedRecipe.time ?? 30, () => {
          // Remove ingredients from inventory
          if (selectedRecipe.ingredients) {
              removeItemsFromInventory(selectedRecipe.ingredients);
          }

          console.log("CRAFTING BUilding!!", selectedRecipe.type, ResourceTypes.Building);

          if (currentLocationBuildingUuid) {
            addStackToABuilding({
                itemId: selectedRecipe.id,
                quantity: 1,
                uuid: uuidv4(),
                itemDef: selectedRecipe,
            });
          } else if (selectedRecipe.type === ResourceTypes.BUILDING) {
            addToGroundBuildings(currRegionIndex, selectedRecipe);
          } else {
            // Add crafted item to inventory
            addItemToInventory(selectedRecipe);
          }
          // spoilInventory(selectedRecipe.time ?? 30);
      });
  };


  return (
    <MacOSModal
      title={recipe.name}
      isOpen={isOpen}
      onClose={onClose}
      initialSize={{ width: 500, height: 550 }}
    >
      <div className="recipe-details-modal">
        <div className="recipe-detail-icon">
          <ItemIcon itemDef={recipe} maxWidth={42}  />
        </div>
        <p>{recipe.description}</p>
        <h4>{getText('Ingredients')}:</h4>
        <ul className="ingredient-list">
          {recipe.ingredients?.map((ingredient) => {
            const available = getIngredientQuantity(ingredient.itemDef.id);
            const hasEnough = available >= ingredient.quantity;
            return (
              <li key={ingredient.itemDef.id} className={hasEnough ? 'has-enough' : 'not-enough'}>
                {Items[ingredient.itemDef.id]?.name} ({available}/{ingredient.quantity})
              </li>
            );
          })}
        </ul>
        <h4>{getText('Result')}:</h4>
        <div className="recipe-result">
          <div className="result-icon">
            <ItemIcon itemDef={recipe} />
          </div>
          <div className="result-info">
            <div className="result-name">{recipe.name}</div>
            <div className="result-description">{recipe.description}</div>
            {recipe.type.name === "Edible" && (
              <div className="result-stats">
                {recipe.food > 0 && <div>🍖 {getText('Food')}: +{recipe.food}</div>}
                {recipe.water > 0 && <div>💧 {getText('Water')}: +{recipe.water}</div>}
                {recipe.energy > 0 && <div>⚡ {getText('Energy')}: +{recipe.energy}</div>}
                {recipe.health > 0 && <div>❤️ {getText('Health')}: +{recipe.health}</div>}
              </div>
            )}
            {recipe.type.name === "Medicinal" && (
              <div className="result-stats">
                <div>❤️ {getText('Health')}: +{recipe.health}</div>
              </div>
            )}
          </div>
        </div>
        <button
          className="craft-button green-btn"
          disabled={!canCraft}
          onClick={() => {
              handleCraft();
              onCraft();
            }
          }
        >
          {getText('Craft')}
        </button>
      </div>
    </MacOSModal>
  );
};

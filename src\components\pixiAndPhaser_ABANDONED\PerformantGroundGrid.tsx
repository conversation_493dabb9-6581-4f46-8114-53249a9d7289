import React, { useState } from 'react';
import { GroundGrid } from './GroundGrid';
import { PixiGroundGrid } from './pixiAndPhaser/PixiGroundGrid';
import { CanvasGroundGrid } from './CanvasGroundGrid';

// Performance implementation options
type ImplementationType = 'dom' | 'pixi' | 'canvas';

interface PerformantGroundGridProps {
  // Optional prop to force a specific implementation
  implementation?: ImplementationType;
  // Show implementation switcher for testing
  showSwitcher?: boolean;
}

export const PerformantGroundGrid: React.FC<PerformantGroundGridProps> = ({
  implementation = 'canvas', // Default to canvas for best performance
  showSwitcher = false,
}) => {
  const [currentImplementation, setCurrentImplementation] = useState<ImplementationType>(implementation);

  const renderImplementation = () => {
    switch (currentImplementation) {
      case 'dom':
        return <GroundGrid />;
      case 'pixi':
        return <PixiGroundGrid />;
      case 'canvas':
        return <CanvasGroundGrid />;
      default:
        return <CanvasGroundGrid />;
    }
  };

  return (
    <div className="performant-ground-grid" style={{ width: '100%', height: '100%' }}>
      {showSwitcher && (
        <div style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          zIndex: 1000,
          background: 'rgba(0, 0, 0, 0.8)',
          padding: '8px',
          borderRadius: '4px',
          display: 'flex',
          gap: '8px',
        }}>
          <button
            onClick={() => setCurrentImplementation('dom')}
            style={{
              padding: '4px 8px',
              background: currentImplementation === 'dom' ? '#4a90e2' : '#333',
              color: 'white',
              border: 'none',
              borderRadius: '2px',
              fontSize: '12px',
              cursor: 'pointer',
            }}
          >
            DOM
          </button>
          <button
            onClick={() => setCurrentImplementation('pixi')}
            style={{
              padding: '4px 8px',
              background: currentImplementation === 'pixi' ? '#4a90e2' : '#333',
              color: 'white',
              border: 'none',
              borderRadius: '2px',
              fontSize: '12px',
              cursor: 'pointer',
            }}
          >
            PixiJS
          </button>
          <button
            onClick={() => setCurrentImplementation('canvas')}
            style={{
              padding: '4px 8px',
              background: currentImplementation === 'canvas' ? '#4a90e2' : '#333',
              color: 'white',
              border: 'none',
              borderRadius: '2px',
              fontSize: '12px',
              cursor: 'pointer',
            }}
          >
            Canvas
          </button>
        </div>
      )}
      {renderImplementation()}
    </div>
  );
};

// Export individual implementations for direct use
export { GroundGrid as DOMGroundGrid } from './GroundGrid';
export { PixiGroundGrid } from './pixiAndPhaser/PixiGroundGrid';
export { CanvasGroundGrid } from './CanvasGroundGrid';

// Performance comparison component for testing
export const GroundGridPerformanceTest: React.FC = () => {
  const [metrics, setMetrics] = useState<{
    dom: { fps: number; renderTime: number } | null;
    pixi: { fps: number; renderTime: number } | null;
    canvas: { fps: number; renderTime: number } | null;
  }>({
    dom: null,
    pixi: null,
    canvas: null,
  });

  const [currentTest, setCurrentTest] = useState<ImplementationType | null>(null);

  const runPerformanceTest = (implementation: ImplementationType) => {
    setCurrentTest(implementation);
    
    // Simple performance measurement
    const startTime = performance.now();
    let frameCount = 0;
    const testDuration = 5000; // 5 seconds
    
    const measureFrame = () => {
      frameCount++;
      const elapsed = performance.now() - startTime;
      
      if (elapsed < testDuration) {
        requestAnimationFrame(measureFrame);
      } else {
        const fps = (frameCount / elapsed) * 1000;
        const renderTime = elapsed / frameCount;
        
        setMetrics(prev => ({
          ...prev,
          [implementation]: { fps, renderTime },
        }));
        setCurrentTest(null);
      }
    };
    
    requestAnimationFrame(measureFrame);
  };

  return (
    <div style={{ padding: '20px', background: 'rgba(0, 0, 0, 0.8)', color: 'white' }}>
      <h3>Ground Grid Performance Test</h3>
      
      <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
        <button
          onClick={() => runPerformanceTest('dom')}
          disabled={currentTest !== null}
          style={{
            padding: '8px 16px',
            background: '#4a90e2',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: currentTest ? 'not-allowed' : 'pointer',
            opacity: currentTest ? 0.5 : 1,
          }}
        >
          Test DOM {currentTest === 'dom' ? '(Running...)' : ''}
        </button>
        
        <button
          onClick={() => runPerformanceTest('pixi')}
          disabled={currentTest !== null}
          style={{
            padding: '8px 16px',
            background: '#4a90e2',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: currentTest ? 'not-allowed' : 'pointer',
            opacity: currentTest ? 0.5 : 1,
          }}
        >
          Test PixiJS {currentTest === 'pixi' ? '(Running...)' : ''}
        </button>
        
        <button
          onClick={() => runPerformanceTest('canvas')}
          disabled={currentTest !== null}
          style={{
            padding: '8px 16px',
            background: '#4a90e2',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: currentTest ? 'not-allowed' : 'pointer',
            opacity: currentTest ? 0.5 : 1,
          }}
        >
          Test Canvas {currentTest === 'canvas' ? '(Running...)' : ''}
        </button>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '20px' }}>
        {(['dom', 'pixi', 'canvas'] as ImplementationType[]).map(impl => (
          <div key={impl} style={{ 
            padding: '15px', 
            background: 'rgba(255, 255, 255, 0.1)', 
            borderRadius: '4px' 
          }}>
            <h4 style={{ margin: '0 0 10px 0', textTransform: 'capitalize' }}>
              {impl === 'pixi' ? 'PixiJS' : impl.toUpperCase()}
            </h4>
            {metrics[impl] ? (
              <div>
                <div>FPS: {metrics[impl]!.fps.toFixed(1)}</div>
                <div>Avg Render Time: {metrics[impl]!.renderTime.toFixed(2)}ms</div>
              </div>
            ) : (
              <div style={{ color: '#888' }}>No data</div>
            )}
          </div>
        ))}
      </div>

      <div style={{ marginTop: '20px' }}>
        <h4>Performance Comparison</h4>
        <PerformantGroundGrid implementation="canvas" showSwitcher={true} />
      </div>
    </div>
  );
};

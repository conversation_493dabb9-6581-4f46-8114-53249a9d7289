import React = require("react");
import { ResourceMetaInfo } from "src/Interfaces";
import { useRootStore } from "../stores/rootStore";
import { WindowManager } from "./WindowManagement/WindowManager";
import { Dock } from "./WindowManagement/Dock";
import { BoatSystem } from "./BoatSystem/BoatSystem";
import { useGroundStore } from "src/stores/groundStore";

// For backward compatibility - will be removed in future updates
export const RootContext = React.createContext(null);

// export const RootProvider = (props : {
//     regionIndex: number,
//     resourceMetaInfosInAllRegions: ResourceMetaInfo[][],
//     biomesList: string[];
//     children: React.ReactNode
// }) => {
//     // Initialize the Zustand store with the provided props
//     const setCurrRegionIndex = useRootStore(state => state.setCurrRegionIndex);
//     const setResourceMetaInfosInAllRegions = useRootStore(state => state.setResourceMetaInfosInAllRegions);
//     const setBiomesList = useRootStore(state => state.setBiomesList);

//     // Set initial values from props
//     React.useEffect(() => {
//         if (props.regionIndex !== undefined) {
//             setCurrRegionIndex(props.regionIndex);
//         }

//         setResourceMetaInfosInAllRegions(props.resourceMetaInfosInAllRegions);
//         setBiomesList(props.biomesList);
//     }, [props.regionIndex, props.resourceMetaInfosInAllRegions, props.biomesList]);

//     // Simply render children - no context provider needed as Zustand handles state
//     return <>{props.children}</>;
// };


export const GameRootPage = (props : {
    regionIndex: number,
    resourceMetaInfosInAllRegions: ResourceMetaInfo[][],
    biomesList: string[];
    isFromLandToWater: boolean;
    isFromWaterToLand: boolean;
}) => {
    if (props.regionIndex === undefined) {
        return null;
    }

    console.log("resourceMetaInfosInAllRegions", props.resourceMetaInfosInAllRegions.length
        , "biomesList", props.biomesList.length
    );
    
    const setCurrRegionIndex = useRootStore(state => state.setCurrRegionIndex);
    const currRegionIndex = useRootStore(state => state.currRegionIndex);
    // const prevRegionIndex = useRootStore(state => state.prevRegionIndex);
    // const setPrevRegionIndex = useRootStore(state => state.setPrevRegionIndex);
    const setResourceMetaInfosInAllRegions = useRootStore(state => state.setResourceMetaInfosInAllRegions);
    const setBiomesList = useRootStore(state => state.setBiomesList);
    // const biomesList = useRootStore(state => state.biomesList);
    
    const [ showBoatDialog, setShowBoatDialog ] = React.useState(false);

    // Set initial values from props
    React.useEffect(() => {
        setResourceMetaInfosInAllRegions(props.resourceMetaInfosInAllRegions);
        setBiomesList(props.biomesList);
        useGroundStore.getState().initializeRegion(props.biomesList.length);
    }, [props.resourceMetaInfosInAllRegions, props.biomesList]);

    React.useEffect(() => {
        // if (props.regionIndex !== undefined) {
            // setPrevRegionIndex(currRegionIndex);
            setCurrRegionIndex(props.regionIndex);
        // }
    }, [props.regionIndex]);

    if (currRegionIndex === null || props.regionIndex === undefined
        // || props.regionIndex == currRegionIndex
    ) {
        return null;
    }

    // console.log("biomesList[currRegionIndex]", biomesList[currRegionIndex]);

    return (
        // <GameRoot
        //     regionIndex={props.regionIndex}
        // />
        <div className="game-root">
            {/* Window Manager handles all the macOS-like windows */}
            <WindowManager />
            {/* Dock for launching windows */}
            <Dock />
            {props.isFromLandToWater && (
                <BoatSystem showBoatDialog={true} />
            )}
        </div>
    );
};

// const GameRoot = (props : {
//     regionIndex: number,
// }) => {

//     if (currRegionIndex === null || props.regionIndex === undefined) {
//         return null;
//     }

//     return (
//     );
// };

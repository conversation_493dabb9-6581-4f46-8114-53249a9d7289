// Demo script to show how to update the status bars
// You can include this in your main JavaScript or call these functions from your game logic

// Function to update a specific circular status bar
function updateStatusBar(statName, percentage) {
    const circle = document.getElementById(`${statName}-circle`);
    if (circle) {
        // Clamp percentage between 0 and 100
        const clampedPercentage = Math.max(0, Math.min(100, percentage));

        // Calculate circle properties
        const radius = 16; // Same as in SVG
        const circumference = 2 * Math.PI * radius;

        // Set up the circle for progress animation
        circle.style.strokeDasharray = circumference;

        // Calculate the offset based on percentage (inverted because we want to show progress)
        const offset = circumference - (clampedPercentage / 100) * circumference;
        circle.style.strokeDashoffset = offset;
    }
}

// Function to update all status bars at once
function updateAllStatusBars(stats) {
    updateStatusBar('health', stats.health || 100);
    updateStatusBar('food', stats.food || 100);
    updateStatusBar('water', stats.water || 100);
    updateStatusBar('energy', stats.energy || 100);
    updateStatusBar('weight', stats.weight || 100);
    updateStatusBar('sanity', stats.sanity || 100);
}

// Demo function to simulate changing stats
function demoStatusBars() {
    let health = 100;
    let food = 100;
    let water = 100;
    let energy = 100;
    let weight = 100;
    let sanity = 100;

    setInterval(() => {
        // Simulate gradual decrease
        health = Math.max(0, health - Math.random() * 2);
        food = Math.max(0, food - Math.random() * 1.5);
        water = Math.max(0, water - Math.random() * 1.8);
        energy = Math.max(0, energy - Math.random() * 2.2);
        weight = Math.max(0, weight - Math.random() * 1.0);
        sanity = Math.max(0, sanity - Math.random() * 1.3);

        // Update the bars
        updateAllStatusBars({
            health: health,
            food: food,
            water: water,
            energy: energy,
            weight: weight,
            sanity: sanity
        });

        // Reset when all bars are low
        if (health < 10 && food < 10 && water < 10 && energy < 10 && weight < 10 && sanity < 10) {
            health = food = water = energy = weight = sanity = 100;
        }
    }, 100);
}

// Initialize status bars when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Set initial values to 100%
    updateAllStatusBars({
        health: 100,
        food: 100,
        water: 100,
        energy: 100,
        weight: 100,
        sanity: 100
    });

    // Uncomment the line below to run the demo
    // demoStatusBars();
});

// Example usage in your game:
// updateStatusBar('health', 75);  // Set health to 75%
// updateStatusBar('food', 50);    // Set food to 50%
// updateAllStatusBars({ health: 80, food: 60, water: 90, energy: 40, weight: 70, sanity: 85 });

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface MacOSWindowProps {
  title: string;
  isOpen: boolean;
  onClose: () => void;
  initialPosition?: { x: number, y: number };
  initialSize?: { width: number, height: number };
  children: React.ReactNode;
  zIndex: number;
  onFocus: () => void;
  onResize?: (size: { width: number, height: number }) => void;
}

export const MacOSWindow: React.FC<MacOSWindowProps> = ({
  title,
  isOpen,
  onClose,
  initialPosition = { x: 50, y: 50 },
  initialSize = { width: 400, height: 300 },
  children,
  zIndex,
  onFocus,
  onResize
}) => {
  const [position, setPosition] = useState(initialPosition);
  const [size, setSize] = useState(initialSize);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeDirection, setResizeDirection] = useState<'right' | 'bottom' | 'corner' | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [resizeStartPos, setResizeStartPos] = useState({ x: 0, y: 0 });
  const [resizeStartSize, setResizeStartSize] = useState({ width: 0, height: 0 });
  const windowRef = useRef<HTMLDivElement>(null);

  // Handle window dragging
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains('window-title')) {
      setIsDragging(true);
      const rect = windowRef.current?.getBoundingClientRect();
      if (rect) {
        setDragOffset({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
      onFocus(); // Bring window to front when starting to drag
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      // Calculate new position
      let newX = e.clientX - dragOffset.x;
      let newY = e.clientY - dragOffset.y;

      // Get viewport dimensions
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Apply boundary constraints
      // Prevent window from going too far left (allow some negative for partial visibility)
      newX = Math.max(-size.width + 100, newX);
      // Prevent window from going too far right
      newX = Math.min(viewportWidth - 100, newX);
      // Prevent window from going above viewport (keep titlebar visible)
      newY = Math.max(0, newY);
      // Prevent window from going too far down (allow some bottom margin)
      newY = Math.min(viewportHeight - 50, newY);

      setPosition({
        x: newX,
        y: newY
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Handle resize start
  const handleResizeStart = (e: React.MouseEvent<HTMLDivElement>, direction: 'right' | 'bottom' | 'corner') => {
    e.stopPropagation();
    setIsResizing(true);
    setResizeDirection(direction);
    setResizeStartPos({ x: e.clientX, y: e.clientY });
    setResizeStartSize({ width: size.width, height: size.height });
    onFocus(); // Bring window to front when starting to resize
  };

  // Handle resize move
  const handleResizeMove = (e: MouseEvent) => {
    if (!isResizing || !resizeDirection) return;

    let newWidth = size.width;
    let newHeight = size.height;

    // Calculate new dimensions based on resize direction
    if (resizeDirection === 'right' || resizeDirection === 'corner') {
      newWidth = Math.max(300, resizeStartSize.width + (e.clientX - resizeStartPos.x));
    }

    if (resizeDirection === 'bottom' || resizeDirection === 'corner') {
      newHeight = Math.max(200, resizeStartSize.height + (e.clientY - resizeStartPos.y));
    }

    // Update size
    setSize({ width: newWidth, height: newHeight });

    // Call onResize callback if provided
    if (onResize) {
      onResize({ width: newWidth, height: newHeight });
    }
  };

  // Handle resize end
  const handleResizeEnd = () => {
    setIsResizing(false);
    setResizeDirection(null);
  };

  // Add and remove event listeners
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    if (isResizing) {
      window.addEventListener('mousemove', handleResizeMove);
      window.addEventListener('mouseup', handleResizeEnd);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
      window.removeEventListener('mousemove', handleResizeMove);
      window.removeEventListener('mouseup', handleResizeEnd);
    };
  }, [isDragging, isResizing, dragOffset, resizeDirection, resizeStartPos, resizeStartSize]);

  // Handle close
  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClose();
  };

  // Handle window click
  const handleWindowClick = () => {
    onFocus();
  };

  return (
    <AnimatePresence>
      {/* {isOpen && ( */}
        <motion.div
          ref={windowRef}
          className="macos-window dark-mode"
          style={{
            left: position.x,
            top: position.y,
            width: size.width,
            height: size.height,
            zIndex,
            // cursor: isDragging ? 'grabbing' : 'default',
            // display: isOpen ? 'flex' : 'none',
            // width: isOpen ? size.width : 0
            // visibility: isOpen ? 'visible' : 'hidden'
          }}
          // transition="0.3s"
          initial={{
            scale: 0,
            display: 'none',
            // opacity: 0 
            transformOrigin: "bottom center",
          }}
          animate={{
            scale: isOpen ? 1 : 0.1,
            // opacity: isOpen ? 1 : 0, 
            display: isOpen ? 'flex' : 'none',
            transition: { duration: 0.3 }
            // transition={{ type: 'spring', damping: 20, stiffness: 300, duration: 6 }}
          }}
          // exit={{
          //   scale: 0.8,
          //   opacity: 0
          // }}
          onClick={handleWindowClick}
        >
          <div
            className="window-titlebar"
            onMouseDown={handleMouseDown}
          >
            <div className="window-controls">
              {/* <button className="window-control close" onClick={handleClose}></button> */}
              {/* <button className="window-control minimize" onClick={handleClose}></button>
              <button className="window-control maximize" onClick={handleClose}></button> */}
            </div>
            <div className="window-title">{title}</div>
            <div className="window-titlebar-spacer" onClick={handleClose}>
              <img className="cancel-icon"
                src="images/ui_icons/cancel.svg" width={16} height={16} style={{filter: "invert(1)"}} />
            </div>
          </div>

          
          <div
            className="window-content scrollable-container"
          >
            {/* <div className="window-content-inner"> */}
            {children}
            {/* </div> */}
          </div>


          {/* <motion.div
            className="window-content scrollable-container"
            animate={{
              opacity: 1,
              height: 'auto'
            }}
            transition={{ duration: 0.2 }}
          >
            <div className="window-content-inner">
            {children}
            </div>
          </motion.div> */}

          {/* Resize handles */}
          <div
            className="resize-handle resize-handle-right"
            onMouseDown={(e: React.MouseEvent<HTMLDivElement>) => handleResizeStart(e, 'right')}
          />
          <div
            className="resize-handle resize-handle-bottom"
            onMouseDown={(e: React.MouseEvent<HTMLDivElement>) => handleResizeStart(e, 'bottom')}
          />
          <div
            className="resize-handle resize-handle-corner"
            onMouseDown={(e: React.MouseEvent<HTMLDivElement>) => handleResizeStart(e, 'corner')}
          />
        </motion.div>
      {/* )} */}
    </AnimatePresence>
  );
};

import React from 'react';
import { PixiGroundGrid } from './pixiAndPhaser/PixiGroundGrid';

// Demo component to showcase the PixiJS implementation
export const PixiGroundGridDemo = () => {
  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px',
      boxSizing: 'border-box',
    }}>
      <div style={{
        width: '90%',
        height: '90%',
        maxWidth: '1200px',
        maxHeight: '800px',
        background: 'rgba(0, 0, 0, 0.3)',
        borderRadius: '8px',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        boxShadow: '0 4px 15px rgba(0, 0, 0, 0.5)',
        overflow: 'hidden',
      }}>
        <div style={{
          padding: '20px',
          borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
          background: 'rgba(0, 0, 0, 0.2)',
        }}>
          <h2 style={{
            color: '#e0e0e0',
            margin: 0,
            fontSize: '24px',
            fontWeight: 'bold',
            textAlign: 'center',
          }}>
            PixiJS Ground Grid - High Performance Inventory System
          </h2>
          <p style={{
            color: '#aaa',
            margin: '10px 0 0 0',
            fontSize: '14px',
            textAlign: 'center',
          }}>
            Drag and drop items between inventory and storage with smooth animations and visual effects
          </p>
        </div>
        
        <div style={{
          height: 'calc(100% - 80px)',
          position: 'relative',
        }}>
          <PixiGroundGrid />
        </div>
      </div>
    </div>
  );
};

// Performance comparison component
export const PerformanceComparison = () => {
  const [usePixi, setUsePixi] = React.useState(true);
  const [renderTime, setRenderTime] = React.useState(0);

  React.useEffect(() => {
    const startTime = performance.now();
    
    // Simulate render completion
    const timer = setTimeout(() => {
      const endTime = performance.now();
      setRenderTime(endTime - startTime);
    }, 100);

    return () => clearTimeout(timer);
  }, [usePixi]);

  return (
    <div style={{
      position: 'absolute',
      top: '10px',
      right: '10px',
      background: 'rgba(0, 0, 0, 0.8)',
      padding: '15px',
      borderRadius: '8px',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      color: '#e0e0e0',
      fontSize: '12px',
      minWidth: '200px',
    }}>
      <h4 style={{ margin: '0 0 10px 0', color: '#4a90e2' }}>Performance Monitor</h4>
      
      <div style={{ marginBottom: '10px' }}>
        <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
          <input
            type="checkbox"
            checked={usePixi}
            onChange={(e) => setUsePixi(e.target.checked)}
            style={{ marginRight: '8px' }}
          />
          Use PixiJS Renderer
        </label>
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Render Time:</strong> {renderTime.toFixed(2)}ms
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Renderer:</strong> {usePixi ? 'PixiJS (WebGL)' : 'DOM (CSS)'}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>FPS:</strong> {Math.round(1000 / Math.max(renderTime, 16))}
      </div>
      
      <div style={{ 
        fontSize: '10px', 
        color: '#aaa', 
        marginTop: '10px',
        borderTop: '1px solid rgba(255, 255, 255, 0.1)',
        paddingTop: '8px',
      }}>
        PixiJS provides significant performance improvements for large inventories with hardware-accelerated rendering.
      </div>
    </div>
  );
};

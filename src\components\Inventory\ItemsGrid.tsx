import React = require("react");
import { InventoryItemStack } from "src/Interfaces";
import { InventoryItemSlot } from "./InventoryItemSlot";
import { DEFAULT_INV_SLOT } from "src/settings";

interface InventoryGridProps {
    itemStacks: InventoryItemStack[];
    selectedStack: InventoryItemStack | null;
    setSelectedStack: (itemStack: InventoryItemStack) => void;
    activeId: string | null;
}

export const ItemsGrid = ({
    itemStacks,
    selectedStack,
    setSelectedStack,
    activeId
}: InventoryGridProps) => {
    console.log("ItemsGrid rerendered");

    // itemStacks should not be null
    if (!itemStacks) {
        // console.log("itemStacks is null");
        console.trace("itemStacks is null");
        return;
    }

    // const equippedItems = useEquipmentStore(state => state.equippedItems);
    // console.log("selectedStack ", selectedStack?.uuid === itemStacks[9]?.uuid);

    return (
        <>
            {new Array(itemStacks.length + 5).fill(null).map((_, index) => (
            // {new Array(DEFAULT_INV_SLOT + (equippedItems.BACKPACK?.itemDef.storageCapacity ?? 0)).fill(null).map((_, index) => (
                <InventoryItemSlot
                    key={index}
                    itemStack={itemStacks[index]}
                    stackIndex={index}
                    isSelected={itemStacks[index] && selectedStack?.uuid === itemStacks[index].uuid}
                    setSelectedStack={setSelectedStack}
                    isActive={`draggable-${index}` === activeId}
                />
            ))}
        </>
    );
};

/* Cooking System Styles */

/* Fire Light Up Modal */
.fire-light-up-modal {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.fire-light-up-modal h3 {
  margin: 0;
  font-size: 18px;
  color: #f5f5f5;
}

.fire-duration {
  background-color: rgba(255, 165, 0, 0.2);
  padding: 10px;
  border-radius: 8px;
  margin-top: 10px;
}

.light-fire-button {
  background-color: #ff6b35;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 16px;
  align-self: center;
}

.light-fire-button:hover {
  background-color: #ff8c5a;
}

.light-fire-button:disabled {
  background-color: #666;
  cursor: not-allowed;
}

/* Inventory item styles */
.inventory-item {
  position: relative;
  transition: opacity 0.2s, transform 0.2s;
}

.inventory-item.depleted {
  opacity: 0.5;
  cursor: not-allowed;
}

.inventory-item:not(.depleted):hover {
  transform: scale(1.05);
}

.item-selected-quantity {
  position: absolute;
  bottom: -5px;
  right: -5px;
  background-color: rgb(85 85 85 / 80%);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 4px;
  white-space: nowrap;
}

/* Cooking Modal */
.cooking-modal {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.cooking-modal h3 {
  margin: 0;
  font-size: 18px;
  color: #f5f5f5;
}

.cooking-controls {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}

.recipes-button {
  background-color: #393939;
  color: white;
  border: none;
  border-radius: 2px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.recipes-button:hover {
  background-color: #5c5c5c;
}

.cooking-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.cooking-slot {
  width: 60px;
  height: 60px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.recipe-preview {
  background-color: rgba(255, 255, 255, 0.05);
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
}

.recipe-result {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
}

.result-icon {
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.result-info {
  flex: 1;
}

.result-name {
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 4px;
}

.result-description {
  font-size: 14px;
  color: #ccc;
  margin-bottom: 8px;
}

.result-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 4px;
  font-size: 14px;
}

.cook-button {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 16px;
  align-self: center;
}

.cook-button:hover {
  background-color: #5dbd61;
}

.cook-button:disabled {
  background-color: #666;
  cursor: not-allowed;
}

/* Recipes Modal */
.recipes-modal {
  padding: 16px;
}

.recipes-list {
  margin-top: 16px;
}

.recipes-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* .recipe-item {
  display: flex;
  align-items: center;
  gap: 7px;
  padding: 12px;
  border-radius: 1px;
  background-color: rgba(255, 255, 255, 0.05);
  margin-bottom: -1px;
  border: var(--border-default-gray);
  margin-right: 10px;
} */

.recipe-requirements {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

.recipe-requirements span {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

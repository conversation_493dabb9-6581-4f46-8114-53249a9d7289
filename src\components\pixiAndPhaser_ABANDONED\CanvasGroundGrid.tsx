import React, { useRef, useEffect, useState, useCallback } from 'react';
import { InventoryItemStack } from 'src/Interfaces';
import { getText } from 'src/i18n';
import { useRootStore } from 'src/stores/rootStore';
import { useGroundStore } from 'src/stores/groundStore';
import { Items } from 'src/enums/resources';

// Grid configuration
interface GridConfig {
  CELL_SIZE: number;
  CELL_PADDING: number;
  INVENTORY_COLS: number;
  STORAGE_COLS: number;
  INVENTORY_ROWS: number;
  STORAGE_ROWS: number;
  TITLE_HEIGHT: number;
  SECTION_SPACING: number;
}

// Slot data interface
interface SlotData {
  type: 'inventory' | 'storage';
  index: number;
  x: number;
  y: number;
  width: number;
  height: number;
  itemStack: InventoryItemStack | null;
}

// Drag state interface
interface DragState {
  isDragging: boolean;
  draggedItem: InventoryItemStack | null;
  sourceType: 'inventory' | 'storage' | null;
  sourceIndex: number;
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
}

export const CanvasGroundGrid: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  const slotsRef = useRef<SlotData[]>([]);
  const dragStateRef = useRef<DragState>({
    isDragging: false,
    draggedItem: null,
    sourceType: null,
    sourceIndex: -1,
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
  });

  // Store state
  const itemStacks = useRootStore(state => state.itemStacks);
  const setItemStacks = useRootStore(state => state.setItemStacks);
  const currRegionIndex = useRootStore(state => state.currRegionIndex);
  const groundStorageStacks = useGroundStore(state => state.allRegionGroundStacks[currRegionIndex]);
  const addStackByIndex = useGroundStore(state => state.addStackByIndex);
  const removeGroundStackByIndex = useGroundStore(state => state.removeStackByIndex);
  const swapGroundStorageStacks = useGroundStore(state => state.swapStacks);
  const swapInventoryStacks = useRootStore(state => state.swapStacks);

  // Selection state
  const [selectedInventoryStack, setSelectedInventoryStack] = useState<InventoryItemStack | null>(null);
  const [selectedGroundStorageStack, setSelectedGroundStorageStack] = useState<InventoryItemStack | null>(null);
  const [hoveredSlot, setHoveredSlot] = useState<SlotData | null>(null);

  // Responsive grid configuration
  const [screenSize, setScreenSize] = useState({ width: window.innerWidth, height: window.innerHeight });

  const getGridConfig = useCallback((): GridConfig => {
    const isMobile = screenSize.width <= 768;
    const isTablet = screenSize.width <= 1024 && screenSize.width > 768;

    return {
      CELL_SIZE: isMobile ? 35 : isTablet ? 40 : 45,
      CELL_PADDING: isMobile ? 1 : 2,
      INVENTORY_COLS: isMobile ? 8 : 10,
      STORAGE_COLS: isMobile ? 8 : 10,
      INVENTORY_ROWS: Math.ceil(itemStacks.length / (isMobile ? 8 : 10)) + 1,
      STORAGE_ROWS: Math.ceil((groundStorageStacks?.length || 0) / (isMobile ? 8 : 10)) + 1,
      TITLE_HEIGHT: isMobile ? 30 : 40,
      SECTION_SPACING: isMobile ? 15 : 20,
    };
  }, [screenSize, itemStacks, groundStorageStacks]);

  // Helper function to get item color based on type
  const getItemColor = (itemDef: any): string => {
    const typeColors: { [key: string]: string } = {
      'food': '#8bc34a',
      'tool': '#795548',
      'weapon': '#f44336',
      'material': '#9e9e9e',
      'consumable': '#2196f3',
    };
    return typeColors[itemDef.type?.id] || '#666666';
  };

  // Helper function to get rarity color
  const getRarityColor = (rarity: number): string => {
    const rarityColors = [
      '#9e9e9e', // Common (gray)
      '#4caf50', // Uncommon (green)
      '#2196f3', // Rare (blue)
      '#9c27b0', // Epic (purple)
      '#ffc107', // Legendary (gold)
    ];
    return rarityColors[Math.min(rarity - 1, rarityColors.length - 1)] || '#9e9e9e';
  };

  // Generate slot data
  const generateSlots = useCallback((config: GridConfig): SlotData[] => {
    const slots: SlotData[] = [];

    // Generate inventory slots
    const totalInventorySlots = Math.max(itemStacks.length + 5, 20);
    for (let i = 0; i < totalInventorySlots; i++) {
      const col = i % config.INVENTORY_COLS;
      const row = Math.floor(i / config.INVENTORY_COLS);
      
      slots.push({
        type: 'inventory',
        index: i,
        x: 10 + col * (config.CELL_SIZE + config.CELL_PADDING),
        y: config.TITLE_HEIGHT + 10 + row * (config.CELL_SIZE + config.CELL_PADDING),
        width: config.CELL_SIZE,
        height: config.CELL_SIZE,
        itemStack: itemStacks[i] || null,
      });
    }

    // Generate storage slots
    if (groundStorageStacks) {
      const totalStorageSlots = Math.max(groundStorageStacks.length + 5, 20);
      const inventoryHeight = config.INVENTORY_ROWS * (config.CELL_SIZE + config.CELL_PADDING);
      const storageStartY = config.TITLE_HEIGHT + 10 + inventoryHeight + config.SECTION_SPACING + config.TITLE_HEIGHT;
      
      for (let i = 0; i < totalStorageSlots; i++) {
        const col = i % config.STORAGE_COLS;
        const row = Math.floor(i / config.STORAGE_COLS);
        
        slots.push({
          type: 'storage',
          index: i,
          x: 10 + col * (config.CELL_SIZE + config.CELL_PADDING),
          y: storageStartY + row * (config.CELL_SIZE + config.CELL_PADDING),
          width: config.CELL_SIZE,
          height: config.CELL_SIZE,
          itemStack: groundStorageStacks[i] || null,
        });
      }
    }

    return slots;
  }, [itemStacks, groundStorageStacks]);

  // Draw a single slot
  const drawSlot = useCallback((
    ctx: CanvasRenderingContext2D,
    slot: SlotData,
    config: GridConfig
  ) => {
    const { x, y, width, height, itemStack } = slot;
    
    // Determine if slot is selected or hovered
    const isSelected = (slot.type === 'inventory' && selectedInventoryStack?.uuid === itemStack?.uuid) ||
                      (slot.type === 'storage' && selectedGroundStorageStack?.uuid === itemStack?.uuid);
    const isHovered = hoveredSlot === slot;
    const isDragOver = dragStateRef.current.isDragging && hoveredSlot === slot;

    // Draw slot background
    ctx.save();
    
    // Background color
    if (isSelected) {
      ctx.fillStyle = 'rgba(74, 144, 226, 0.8)';
    } else if (isDragOver) {
      ctx.fillStyle = 'rgba(74, 144, 226, 0.4)';
    } else if (isHovered && itemStack) {
      ctx.fillStyle = 'rgba(221, 221, 221, 0.3)';
    } else {
      ctx.fillStyle = '#1a1a1a';
    }
    
    // Draw rounded rectangle background
    ctx.beginPath();
    ctx.roundRect(x, y, width, height, 2);
    ctx.fill();
    
    // Draw border
    ctx.strokeStyle = '#3a3a3a';
    ctx.lineWidth = 1;
    ctx.stroke();
    
    // Draw inner shadow
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.beginPath();
    ctx.roundRect(x + 1, y + 1, width - 2, height - 2, 1);
    ctx.fill();

    // Draw item if present
    if (itemStack) {
      const itemDef = Items[itemStack.itemId];
      if (itemDef) {
        // Draw item icon (simplified colored rectangle)
        ctx.fillStyle = getItemColor(itemDef);
        ctx.beginPath();
        ctx.roundRect(x + 5, y + 5, 35, 35, 2);
        ctx.fill();

        // Draw quantity if > 1
        if (itemStack.quantity > 1) {
          // Quantity background
          ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
          ctx.beginPath();
          ctx.arc(x + width - 12, y + height - 12, 10, 0, Math.PI * 2);
          ctx.fill();

          // Quantity text
          ctx.fillStyle = '#ffffff';
          ctx.font = 'bold 10px Arial';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(itemStack.quantity.toString(), x + width - 12, y + height - 12);
        }

        // Draw rarity border if applicable
        if (itemDef.rarity && itemDef.rarity > 1) {
          ctx.strokeStyle = getRarityColor(itemDef.rarity);
          ctx.lineWidth = 2;
          ctx.globalAlpha = 0.8;
          ctx.beginPath();
          ctx.roundRect(x + 4, y + 4, 37, 37, 3);
          ctx.stroke();
          ctx.globalAlpha = 1;
        }
      }
    }

    // Draw selection glow effect
    if (isSelected) {
      ctx.shadowColor = '#4a90e2';
      ctx.shadowBlur = 8;
      ctx.strokeStyle = '#4a90e2';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.roundRect(x - 1, y - 1, width + 2, height + 2, 3);
      ctx.stroke();
      ctx.shadowBlur = 0;
    }

    // Scale effect for hover
    if (isHovered && itemStack && !dragStateRef.current.isDragging) {
      ctx.scale(1.05, 1.05);
      ctx.translate(-x * 0.05 / 2, -y * 0.05 / 2);
    }

    ctx.restore();
  }, [selectedInventoryStack, selectedGroundStorageStack, hoveredSlot, getItemColor, getRarityColor]);

  // Draw drag sprite
  const drawDragSprite = useCallback((ctx: CanvasRenderingContext2D) => {
    const dragState = dragStateRef.current;
    if (!dragState.isDragging || !dragState.draggedItem) return;

    const itemDef = Items[dragState.draggedItem.itemId];
    if (!itemDef) return;

    ctx.save();
    ctx.globalAlpha = 0.8;
    ctx.translate(dragState.currentX, dragState.currentY);
    ctx.scale(1.1, 1.1);

    // Draw drag background
    ctx.fillStyle = 'rgba(74, 144, 226, 0.8)';
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.roundRect(-22, -22, 44, 44, 4);
    ctx.fill();
    ctx.stroke();

    // Draw item icon
    ctx.fillStyle = getItemColor(itemDef);
    ctx.beginPath();
    ctx.roundRect(-16, -16, 32, 32, 2);
    ctx.fill();

    // Draw quantity if > 1
    if (dragState.draggedItem.quantity > 1) {
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 12px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(dragState.draggedItem.quantity.toString(), 15, 15);
    }

    ctx.restore();
  }, [getItemColor]);

  // Main render function
  const render = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const config = getGridConfig();

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set high DPI scaling
    const dpr = window.devicePixelRatio || 1;
    ctx.scale(dpr, dpr);

    // Draw titles
    const titleFontSize = screenSize.width <= 768 ? 14 : 16;
    ctx.fillStyle = '#e0e0e0';
    ctx.font = `bold ${titleFontSize}px Arial`;
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    ctx.fillText(getText('Inventory'), 10, 10);

    const inventoryHeight = config.INVENTORY_ROWS * (config.CELL_SIZE + config.CELL_PADDING);
    const storageY = config.TITLE_HEIGHT + 10 + inventoryHeight + config.SECTION_SPACING;
    ctx.fillText(getText('Storage'), 10, storageY);

    // Draw all slots
    slotsRef.current.forEach(slot => {
      drawSlot(ctx, slot, config);
    });

    // Draw drag sprite
    drawDragSprite(ctx);
  }, [getGridConfig, screenSize, drawSlot, drawDragSprite]);

  // Animation loop
  const animate = useCallback(() => {
    render();
    animationFrameRef.current = requestAnimationFrame(animate);
  }, [render]);

  // Find slot at coordinates
  const findSlotAt = useCallback((x: number, y: number): SlotData | null => {
    return slotsRef.current.find(slot =>
      x >= slot.x && x <= slot.x + slot.width &&
      y >= slot.y && y <= slot.y + slot.height
    ) || null;
  }, []);

  // Handle mouse events
  const handleMouseDown = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const slot = findSlotAt(x, y);
    if (slot && slot.itemStack) {
      dragStateRef.current = {
        isDragging: true,
        draggedItem: slot.itemStack,
        sourceType: slot.type,
        sourceIndex: slot.index,
        startX: x,
        startY: y,
        currentX: x,
        currentY: y,
      };

      // Set selection
      if (slot.type === 'inventory') {
        setSelectedInventoryStack(slot.itemStack);
      } else {
        setSelectedGroundStorageStack(slot.itemStack);
      }
    }
  }, [findSlotAt]);

  const handleMouseMove = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Update drag position
    if (dragStateRef.current.isDragging) {
      dragStateRef.current.currentX = x;
      dragStateRef.current.currentY = y;
    }

    // Update hovered slot
    const slot = findSlotAt(x, y);
    setHoveredSlot(slot);
  }, [findSlotAt]);

  const handleMouseUp = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const dragState = dragStateRef.current;
    if (!dragState.isDragging) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const targetSlot = findSlotAt(x, y);

    if (targetSlot &&
        !(dragState.sourceType === targetSlot.type && dragState.sourceIndex === targetSlot.index)) {

      // Handle different drop scenarios
      if (dragState.sourceType === 'inventory' && targetSlot.type === 'storage') {
        // Move from inventory to storage
        const newItemStacks = [...itemStacks];
        newItemStacks[dragState.sourceIndex] = null;
        setItemStacks(newItemStacks);
        addStackByIndex(currRegionIndex, targetSlot.index, dragState.draggedItem!);
      } else if (dragState.sourceType === 'storage' && targetSlot.type === 'inventory') {
        // Move from storage to inventory
        const newItemStacks = [...itemStacks];
        newItemStacks[targetSlot.index] = dragState.draggedItem!;
        setItemStacks(newItemStacks);
        removeGroundStackByIndex(currRegionIndex, dragState.sourceIndex);
      } else if (dragState.sourceType === 'storage' && targetSlot.type === 'storage') {
        // Swap within storage
        swapGroundStorageStacks(currRegionIndex, dragState.sourceIndex, targetSlot.index);
      } else if (dragState.sourceType === 'inventory' && targetSlot.type === 'inventory') {
        // Swap within inventory
        swapInventoryStacks(dragState.sourceIndex, targetSlot.index);
      }
    }

    // Reset drag state
    dragStateRef.current = {
      isDragging: false,
      draggedItem: null,
      sourceType: null,
      sourceIndex: -1,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
    };
  }, [findSlotAt, itemStacks, setItemStacks, addStackByIndex, currRegionIndex,
      removeGroundStackByIndex, swapGroundStorageStacks, swapInventoryStacks]);

  // Initialize canvas and start animation
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Set canvas size with high DPI support
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    canvas.style.width = rect.width + 'px';
    canvas.style.height = rect.height + 'px';

    // Start animation loop
    animate();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [animate]);

  // Update slots when data changes
  useEffect(() => {
    const config = getGridConfig();
    slotsRef.current = generateSlots(config);
  }, [itemStacks, groundStorageStacks, generateSlots, getGridConfig]);

  // Handle screen resize
  useEffect(() => {
    const handleResize = () => {
      setScreenSize({ width: window.innerWidth, height: window.innerHeight });

      const canvas = canvasRef.current;
      if (canvas) {
        const dpr = window.devicePixelRatio || 1;
        const rect = canvas.getBoundingClientRect();
        canvas.width = rect.width * dpr;
        canvas.height = rect.height * dpr;
        canvas.style.width = rect.width + 'px';
        canvas.style.height = rect.height + 'px';
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Early return if data not ready
  if (!itemStacks || !groundStorageStacks) {
    return null;
  }

  return (
    <div className="canvas-ground-grid storage-modal">
      <canvas
        ref={canvasRef}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        style={{
          width: '100%',
          height: '100%',
          borderRadius: '4px',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          background: 'rgba(0, 0, 0, 0.1)',
          cursor: dragStateRef.current.isDragging ? 'grabbing' : 'default',
        }}
      />
    </div>
  );
};

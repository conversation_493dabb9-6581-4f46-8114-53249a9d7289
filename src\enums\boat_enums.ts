import { Boat } from '../Interfaces';
import { ResourceTypes } from './common_enum';
import { MATERIALS } from './Materials';

export const Boats: { [key: string]: Boat } = {
    raft: {
        id: 'raft',
        icon: '🚣‍♂️',
        name: 'boat_raft',
        description: 'desc_boat_raft',
        durability: 50,
        maxDurability: 50,
        weightCap: 100,
        weight: 20,
        speed: 0.7,
        waterproof: 0.6,
        insulation: 0.3,
        thermalShield: 0.2,
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Grass, quantity: 1 },
        ],
        type: ResourceTypes.TRANSPORT,
        time: 10,
    },
    canoe: {
        id: 'canoe',
        icon: '🚣‍♂️',
        name: 'boat_canoe',
        description: 'desc_boat_canoe',
        durability: 75,
        maxDurability: 75,
        weightCap: 150,
        weight: 30,
        speed: 0.9,
        waterproof: 0.8,
        insulation: 0.5,
        thermalShield: 0.4,
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Grass, quantity: 1 },
        ],
        type: ResourceTypes.TRANSPORT,
        time: 10,
    },
    fishing_boat: {
        id: 'fishing_boat',
        icon: '🚣‍♂️',
        name: 'boat_fishing_boat',
        description: 'desc_boat_fishing_boat',
        durability: 150,
        maxDurability: 150,
        weightCap: 300,
        weight: 80,
        speed: 0.8,
        waterproof: 0.9,
        insulation: 0.7,
        thermalShield: 0.6,
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Grass, quantity: 1 },
        ],
        type: ResourceTypes.TRANSPORT,
        time: 10,
    },
    sailboat: {
        id: 'sailboat',
        icon: 'images/transports/sailboat.png',
        get name() { return getText('boat_sailboat'); },
        get description() { return getText('desc_boat_sailboat'); },
        durability: 200,
        maxDurability: 200,
        weightCap: 400,
        weight: 100,
        speed: 1.2,
        waterproof: 0.95,
        insulation: 0.9,
        thermalShield: 0.8,
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Grass, quantity: 1 },
        ],
        type: ResourceTypes.TRANSPORT,
        time: 10,
    }
}; 
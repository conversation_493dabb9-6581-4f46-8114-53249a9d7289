<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>A Turn Based Survival Game</title>
  <!-- <link rel="shortcut icon" href="/favicon.ico" /> -->
  <!-- <link rel="apple-touch-icon" href="/red%20blob%202d.png" /> -->
  <!-- <link rel="canonical" href="https://www.redblobgames.com/maps/mapgen2/embed.html" /> -->
  <meta name="twitter:creator" content="@redblobgames" />
  <!-- <link rel="stylesheet" href="https://unpkg.com/@radix-ui/themes/styles.css"> -->
  <!-- <link 
    rel="stylesheet" 
    href="node_modules/@radix-ui/themes/styles.css"
  > -->
  <link rel="stylesheet" href="styles/main.css">
  <!-- <link rel="stylesheet" href="build/css/main.css"> -->
  <!-- <link rel="stylesheet" href="build\js\_bundle.css"> -->

  <style>
    /* Weather system styles */
    #weather-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 999;
    }
    
    /* Rain animation */
    @keyframes rainFall {
        0% { 
            transform: translateY(-20px);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        95% {
            opacity: 1;
        }
        100% { 
            transform: translateY(100vh);
            opacity: 0;
        }
    }
    
    /* Storm animation */
    @keyframes stormFall {
        0% { 
            transform: translateY(-30px) rotate(-15deg);
            opacity: 0;
        }
        5% {
            opacity: 1;
        }
        95% {
            opacity: 1;
        }
        100% { 
            transform: translateY(100vh) rotate(-15deg);
            opacity: 0;
        }
    }
    
    /* Lightning animation */
    @keyframes lightningFlash {
        0%, 96%, 98%, 100% {
            opacity: 0;
        }
        97%, 97.5% {
            opacity: 0.8;
        }
        99% {
            opacity: 0.4;
        }
    }
    
    /* Fog animation */
    @keyframes fogAnimation {
        0% { opacity: 0; }
        50% { opacity: 1; }
        100% { opacity: 0; }
    }
    
    /* Rain drop styles */
    .rain-drop {
        position: absolute;
        background: linear-gradient(to bottom, rgba(255,255,255,0.1), rgba(200,230,255,0.8));
        border-radius: 0 0 5px 5px;
        box-shadow: 0 0 5px rgba(200,230,255,0.3);
    }
    
    /* Storm drop styles */
    .storm-drop {
        position: absolute;
        background: linear-gradient(to bottom, rgba(255,255,255,0.1), rgba(200,230,255,0.8));
        border-radius: 0 0 5px 5px;
        box-shadow: 0 0 8px rgba(200,230,255,0.5);
    }
  </style>

</head>

<body>
  <main>

    <div class="top-panel">

    </div>

    <div id="settingsButton">
      <img src = "images/ui_icons/setting.svg" style="width: 70%;height: auto;filter:invert(0.8)"/>
    </div>

    
    <div id="move-buttons-container">
      <div id="moveBtn" class="panel-btn">
          <img src="images/ui_icons/confirm.svg" width=15 height=15 style="filter: invert(1);" />
          <div id="moveButtonText"></div>
      </div>
      <div id="cancelMoveBtn" class="panel-btn">
          <img class="cancel-icon" src="images/ui_icons/cancel.svg" width=14 height=14 style="filter: invert(1);" />
          <div id="cancelMoveButtonText"></div>
      </div>
    </div>
    
      
    <div id="clockContainer" class="ui-panel">
      <div id="day-time-display">
      <!-- style="display: grid; grid-template-columns: 1fr 2px 1fr; "> -->
        <div id="season-display" class="weather-display"></div>
        <!-- <div class="grid-divider"></div> -->
        <div id="weatherDisplay"></div>
      </div>

      
      <!-- <div class="grid">
        <div id="day-display"></div>
        <div class="grid-divider"></div>
        <div id="time-display"></div>
        <div class="grid-divider"></div>
        <div id="feels" style="text-align: center; font-size: 16px;">20 °C</div>
      </div> -->

      <div id="season-weather-display">
        <div id="day-display"></div>
        <div id="time-display"></div>
        <div id="feels" style="text-align: center; font-size: 16px;">20 °C</div>
      </div>

      <div id="time-bar-container">
        <div id="time-bar"></div>
        <div id="time-indicator"></div>
      </div>

    </div>
    
    <div id="statusContainer">

      <!-- Status bars with icons -->
      <div class="status-bars ui-panel">

        <div class="status-bar-item">
          <div class="status-bar-wrapper">
            <div class="circular-progress" id="health-progress">
              <svg viewBox="0 0 40 40">
                <circle class="progress-bg" cx="20" cy="20" r="16"></circle>
                <circle class="progress-fill" cx="20" cy="20" r="16" id="health-circle"></circle>
              </svg>
              <div class="center-icon">
                <img src="images/ui_icons/heart.svg" alt="Health"/>
              </div>
              <div class="momentum-icon">
                <img src="images/ui_icons/double-arrow-up.svg" class="status-icon-img"/>
              </div>
            </div>
          </div>
        </div>

        <div class="status-bar-item">
          <div class="status-bar-wrapper">
            <div class="circular-progress" id="food-progress">
              <svg viewBox="0 0 40 40">
                <circle class="progress-bg" cx="20" cy="20" r="16"></circle>
                <circle class="progress-fill" cx="20" cy="20" r="16" id="food-circle" class="status-circle"></circle>
              </svg>
              <div class="center-icon">
                <img src="images/ui_icons/stomach.png" alt="Food"/>
              </div>
              <div class="momentum-icon">
                <img src="images/ui_icons/chevron-down.svg" class="status-icon-img"/>
              </div>
            </div>
          </div>
          <div class="status-num">
            100
          </div>
        </div>

        <div class="status-bar-item">
          <div class="status-bar-wrapper">
            <div class="circular-progress" id="water-progress">
              <svg viewBox="0 0 40 40">
                <circle class="progress-bg" cx="20" cy="20" r="16"></circle>
                <circle class="progress-fill" cx="20" cy="20" r="16" id="water-circle" class="status-circle"></circle>
              </svg>
              <div class="center-icon">
                <img src="images/ui_icons/water.png" alt="Water"/>
              </div>
              <div class="momentum-icon">
                <img src="images/ui_icons/chevron-down.svg" class="status-icon-img"/>
              </div>
            </div>
          </div>
        </div>

        <div class="status-bar-item">
          <div class="status-bar-wrapper">
            <div class="circular-progress" id="energy-progress">
              <svg viewBox="0 0 40 40">
                <circle class="progress-bg" cx="20" cy="20" r="16"></circle>
                <circle class="progress-fill" cx="20" cy="20" r="16" id="energy-circle" class="status-circle"></circle>
              </svg>
              <div class="center-icon">
                <img src="images/ui_icons/lightning.svg" alt="Energy"/>
              </div>
              <div class="momentum-icon">
                <img src="images/ui_icons/chevron-down.svg" class="status-icon-img"/>
              </div>
            </div>
          </div>
        </div>

        <div class="status-bar-item">
          <div class="status-bar-wrapper">
            <div class="circular-progress" id="weight-progress">
              <svg viewBox="0 0 40 40">
                <circle class="progress-bg" cx="20" cy="20" r="16"></circle>
                <circle class="progress-fill" cx="20" cy="20" r="16" id="weight-circle" class="status-circle"></circle>
              </svg>
              <div class="center-icon">
                <img src="images/ui_icons/muscle.png" alt="Weight"/>
              </div>
              <div class="momentum-icon">
                <img src="images/ui_icons/minus.svg" class="status-icon-img"/>
              </div>
            </div>
          </div>
        </div>


        <div class="status-bar-item">
          <div class="status-bar-wrapper">
            <div class="circular-progress" id="sanity-progress">
              <svg viewBox="0 0 40 40">
                <circle class="progress-bg" cx="20" cy="20" r="16"></circle>
                <circle class="progress-fill" cx="20" cy="20" r="16" id="sanity-circle" class="status-circle"></circle>
              </svg>
              <div class="center-icon">
                <img src="images/ui_icons/depression.png" alt="Sanity"/>
              </div>
              <div class="momentum-icon">
                <img src="images/ui_icons/minus.svg" class="status-icon-img"/>
              </div>
            </div>
          </div>
        </div>
        


      </div>
    </div>


    <div  id="bottom-overlay">
      <canvas id="gameCanvas"></canvas>

      <div id="output"></div>
    
      <div id="ui" style="display: none;">
        <div class="row">
        </div>

        <div class="row">
          <div>
            <label>
              Seed:
              <input id="seed" type="number" min="0" placeholder="number" style="width:5em" />
            </label>
            <button onclick="prevSeed();event.preventDefault()" ontouchmove="prevSeed();event.preventDefault()">-</button>
            <button onclick="nextSeed();event.preventDefault()" ontouchmove="nextSeed();event.preventDefault()">+</button>
          </div>
          <div>
            <label>
              Variant:
              <input id="variant" type="number" min="0" max="9" style="width:3em" />
            </label>
            <button onclick="prevVariant();event.preventDefault()"
              ontouchmove="prevVariant();event.preventDefault()">-</button>
            <button onclick="nextVariant();event.preventDefault()"
              ontouchmove="nextVariant();event.preventDefault()">+</button>
          </div>
        </div>

        <div id="group-sliders" class="row">
          <label id="label-rainfall"><span>Dry</span> <input type="range" id="rainfall" list="tickmarks" min="-1" max="1"
              step="0.05"> <span>Wet</span></label>
          <label id="label-north-temperature"><span>N-Cold</span> <input type="range" id="north-temperature"
              list="tickmarks" min="-1.5" max="1.5" step="0.05"> <span>N-Hot</span></label>
          <label id="label-south-temperature"><span>S-Cold</span> <input type="range" id="south-temperature"
              list="tickmarks" min="-1.5" max="1.5" step="0.05"> <span>S-Hot</span></label>
          <label id="label-persistence"><span>Jagged</span> <input type="range" id="persistence" list="tickmarks" min="-1"
              max="1" step="0.05"> <span>Smooth</span></label>
        </div>

        <div id="group-region-count" class="row clickable-labels">
          <span>Number of regions:</span>
          <label><input type="radio" id="size-tiny" name="size" value="tiny"> tiny</label>
          <label><input type="radio" id="size-small" name="size" value="small"> small</label>
          <label><input type="radio" id="size-medium" name="size" value="medium"> medium</label>
          <label><input type="radio" id="size-large" name="size" value="large"> large</label>
          <label><input type="radio" id="size-huge" name="size" value="huge"> huge</label>
          <!-- <label><input type="radio" id="size-ginormous" name="size" value="ginormous"> ginormous</label> -->
        </div>

        <div class="row clickable-labels">
          <span>Rendering:</span>
          <label><input type="checkbox" id="noisy-edges"> noisy edges</label>
          <label><input type="checkbox" id="noisy-fills"> noisy fills</label>
          <label><input type="checkbox" id="icons"> icons</label>
          <label><input type="checkbox" id="biomes"> biomes</label>
          <label><input type="checkbox" id="lighting"> lighting</label>
          <span>Right click to save image</span>
          <div>or <a id="url" target="_top" href="https://www.redblobgames.com/maps/mapgen2/">link to this map</a></div>
        </div>

        <div class="row" style="text-align:center">
          <div><span>from <a rel="author home copyright" href="//www.redblobgames.com/maps/mapgen2/"
                style="text-decoration:none;color:#a44" target="_blank">
                Red Blob Games
              </a></span></div>
        </div>
      </div>

      <!-- <div id="terrainInfoDisplayBottomLeft"></div> -->
    
    </div>

    <div id="react-root"></div>

    <div id="fadeLabel" class="fade-label">Saved</div>

    <script src="build/js/_bundle.js"></script>
    <!-- Status bars demo script -->
    <script src="status-bars-demo.js"></script>

    <datalist id="tickmarks">
      <option value="-0.5" />
      <option value="0" />
      <option value="0.5" />
    </datalist>

    
    <!-- <img id="location-img" src="images/ui_icons/location.png" style="position: absolute; width: 30px; height: 30px; z-index: 99999; display: none; filter: invert(1);" /> -->

    <div id="tooltip-popover" class="item-description-popover">
      <div id="tooltip-content"></div>
      <!-- <div id="popover-arrow" class="popover-arrow"></div> -->
    </div>

    <!-- <div id="progress-overlay">
        <div class="progress-container">
            <div class="progress-text"></div>
            <div class="progress-bar-container">
                <div id="progress-bar"></div>
            </div>
        </div>
    </div> -->

  </main>
</body>
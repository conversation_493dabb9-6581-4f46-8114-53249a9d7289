/*
 * From http://www.redblobgames.com/maps/mapgen2/
 * Copyright 2017 Red Blob Games <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *      http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import * as util       from './util';
import * as Water      from './water';
import * as Elevation  from './elevation';
import * as Rivers     from './rivers';
import * as Moisture   from './moisture';
import * as Biomes     from './biomes';
import * as NoisyEdges from './noisy-edges';

import { getRandomResources } from './enums/resources.js';

// Spatial grid for efficient region lookups
class SpatialGrid {
    constructor(worldSize, cellSize) {
        this.cellSize = cellSize;
        this.gridSize = Math.ceil(worldSize / cellSize);
        this.cells = Array(this.gridSize).fill().map(() => 
            Array(this.gridSize).fill().map(() => new Map())
        );
    }

    getCellCoords(x, y) {
        return [
            Math.floor(x / this.cellSize),
            Math.floor(y / this.cellSize)
        ];
    }

    insert(x, y, regionId) {
        const [cellX, cellY] = this.getCellCoords(x, y);
        if (cellX >= 0 && cellX < this.gridSize && cellY >= 0 && cellY < this.gridSize) {
            this.cells[cellX][cellY].set(`${x},${y}`, regionId);
        }
    }

    findClosestRegion(x, y) {
        const [cellX, cellY] = this.getCellCoords(x, y);
        
        if (cellX < 0 || cellX >= this.gridSize || cellY < 0 || cellY >= this.gridSize) {
            return 0; // Return default region if out of bounds
        }

        let closestRegion = 0;
        let closestDistance = Infinity;

        // Check regions in current cell and adjacent cells
        for (let i = -1; i <= 1; i++) {
            for (let j = -1; j <= 1; j++) {
                const checkX = cellX + i;
                const checkY = cellY + j;
                
                if (checkX >= 0 && checkX < this.gridSize && 
                    checkY >= 0 && checkY < this.gridSize) {
                    
                    for (const [coords, regionId] of this.cells[checkX][checkY]) {
                        const [regionX, regionY] = coords.split(',').map(Number);
                        const dx = x - regionX;
                        const dy = y - regionY;
                        const distance = dx * dx + dy * dy;

                        if (distance < closestDistance) {
                            closestDistance = distance;
                            closestRegion = regionId;
                        }
                    }
                }
            }
        }

        return closestRegion;
    }
}

/**
 * Map generator
 *
 * Map coordinates are 0 ≤ x ≤ 1000, 0 ≤ y ≤ 1000.
 *
 * mesh: TriangleMesh
 * noisyEdgeOptions: {length, amplitude, seed}
 * makeRandInt: function(seed) -> function(N) -> an int from 0 to N-1
 */
export class WorldMap {
    constructor(mesh, noisyEdgeOptions, makeRandInt) {
        this.mesh = mesh;
        this.makeRandInt = makeRandInt;
        this.lines_s = NoisyEdges.assign_lines_s(
            [],
            this.mesh,
            noisyEdgeOptions,
            this.makeRandInt(noisyEdgeOptions.seed)
        );

        // Initialize spatial grid for efficient region lookups
        this.spatialGrid = new SpatialGrid(1000, 25); // Grid cell size of 50 units
        console.log("mesh.numRegions", mesh.numRegions);
        
        // Pre-compute and store all region coordinates
        for (let r = 0; r < mesh.numRegions; r++) {
            const x = mesh.x_of_r(r);
            const y = mesh.y_of_r(r);
            this.spatialGrid.insert(x, y, r);
        }

        this.water_r = [];
        this.ocean_r = [];
        this.coastdistance_t = [];
        this.elevation_t = [];
        this.s_downslope_t = [];
        this.elevation_r = [];
        this.flow_s = [];
        this.waterdistance_r = [];
        this.moisture_r = [];
        this.coast_r = [];
        this.temperature_r = [];
        /** @type string[] */
        this.biome_r = [];
        this.resources_r = [];
    }

    // Add efficient region lookup method
    findRegionAt(x, y) {
        return this.spatialGrid.findClosestRegion(x, y);
    }

    calculate(options) {
        options = Object.assign({
            noise: null, // required: function(nx, ny) -> number from -1 to +1
            shape: {round: 0.5, inflate: 0.4, amplitudes: [1/2, 1/4, 1/8, 1/16]},
            numRivers: 30,
            drainageSeed: 0,
            riverSeed: 0,
            noisyEdge: {length: 10, amplitude: 0.2, seed: 0},
            biomeBias: {north_temperature: 0, south_temperature: 0, moisture: 0},
        }, options);

        Water.assign_water_r(this.water_r, this.mesh, options.noise, options.shape);
        Water.assign_ocean_r(this.ocean_r, this.mesh, this.water_r);
        
        Elevation.assign_elevation_t(
            this.elevation_t, this.coastdistance_t, this.s_downslope_t,
            this.mesh,
            this.ocean_r, this.water_r, this.makeRandInt(options.drainageSeed)
        );
        Elevation.redistribute_elevation_t(this.elevation_t, this.mesh);
        Elevation.assign_elevation_r(this.elevation_r, this.mesh, this.elevation_t, this.ocean_r);

        this.t_spring = Rivers.find_t_spring(this.mesh, this.water_r, this.elevation_t);
        util.randomShuffle(this.t_spring, this.makeRandInt(options.riverSeed));
        
        this.t_river = this.t_spring.slice(0, options.numRivers);
        // console.log("this.t_river", this.t_river, this.t_spring, this.flow_s);
        Rivers.assign_flow_s(this.flow_s, this.mesh, this.s_downslope_t, this.t_river);
        
        Moisture.assign_moisture_r(
            this.moisture_r, this.waterdistance_r,
            this.mesh,
            this.water_r, Moisture.find_moisture_r_seeds(this.mesh, this.flow_s, this.ocean_r, this.water_r)
        );
        Moisture.redistribute_moisture_r(this.moisture_r, this.mesh, this.water_r,
                                         options.biomeBias.moisture, 1 + options.biomeBias.moisture);

        Biomes.assign_coast_r(this.coast_r, this.mesh, this.ocean_r);
        Biomes.assign_temperature_r(
            this.temperature_r,
            this.mesh,
            this.elevation_r,
            options.biomeBias.north_temperature, options.biomeBias.south_temperature
        );
        Biomes.assign_biome_r(
            this.biome_r,
            this.mesh,
            this.ocean_r, this.water_r, this.coast_r, this.temperature_r, this.moisture_r
        );

        this.createResources();
    }

    createResources() {
        for (let r = 0; r < this.mesh.numRegions; r++) {
            this.resources_r[r] = getRandomResources(this.biome_r[r]);
        }
    }
}

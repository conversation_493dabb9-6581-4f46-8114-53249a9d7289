import React = require("react");
import { getText } from "src/i18n";
import { showPromptOverlay } from "src/util";
import { CraftableItem } from "src/Interfaces";
import { ItemIcon } from "./common";
import { useRootStore } from "../stores/rootStore";
import { RecipeDetailsModal } from "./RecipeDetailsModal";
import { CRAFTABLE_BUILDINGS } from "src/enums/building_enums_new";


// Main crafting component
export const BuildList: React.FC = React.memo(() => {

    // Use Zustand store instead of context
    const itemStacks = useRootStore(state => state.itemStacks);

    // State for the crafting system
    const [selectedRecipe, setSelectedRecipe] = React.useState<CraftableItem | null>(null);
    const [showRecipeModal, setShowRecipeModal] = React.useState(false);

    // Function to check if the player has the required ingredients for a recipe
    const checkIngredients = React.useCallback((recipe: CraftableItem): boolean => {
        if (!recipe.ingredients) return true;

        for (const ingredient of recipe.ingredients) {
            const totalQuantity = getIngredientQuantity(ingredient.itemDef.id);
            if (totalQuantity < ingredient.quantity) {
                return false;
            }
        }
        return true;
    }, [itemStacks]);

    // Function to get the quantity of an ingredient in the inventory
    const getIngredientQuantity = React.useCallback((ingredientId: string): number => {
        return itemStacks.reduce((total, stack) => {
            if (stack && stack.itemId === ingredientId) {
                return total + stack.quantity;
            }
            return total;
        }, 0);
    }, [itemStacks]);

    // Function to handle recipe selection
    const handleRecipeSelect = (recipe: CraftableItem) => {
        setSelectedRecipe(recipe);
        setShowRecipeModal(true);
    };

    return (
        <>
            <div id="recipeList">
                <RecipeList
                    recipes={CRAFTABLE_BUILDINGS}
                    checkIngredients={checkIngredients}
                    selectedRecipe={selectedRecipe}
                    onSelectRecipe={handleRecipeSelect}
                />
            </div>

            {/* Recipe Details Modal */}
            {selectedRecipe && (
                <RecipeDetailsModal
                    recipe={selectedRecipe}
                    isOpen={showRecipeModal}
                    onClose={() => {
                        setShowRecipeModal(false);
                        setSelectedRecipe(null);
                    }}
                    canCraft={checkIngredients(selectedRecipe)}
                    getIngredientQuantity={getIngredientQuantity}
                    selectedRecipe={selectedRecipe}
                    onCraft={() => {
                        // Show success message
                        showPromptOverlay(getText('Crafted') + `: ${selectedRecipe.name}`);
                        setShowRecipeModal(false);
                        setSelectedRecipe(null);
                    }}
                />
            )}
        </>
    );
});




// Component for the recipe list
const RecipeList: React.FC<{
    recipes: { [key: string]: CraftableItem };
    checkIngredients: (recipe: CraftableItem) => boolean;
    selectedRecipe: CraftableItem | null;
    onSelectRecipe: (recipe: CraftableItem) => void;
}> = ({ recipes, checkIngredients, selectedRecipe, onSelectRecipe }) => {
    return (
        <div id="recipeListContent" className="scrollable-container">
            {Object.values(recipes).length > 0 ? (
                Object.values(recipes).map((recipe) => (
                    <RecipeItem
                        key={recipe.id}
                        recipe={recipe}
                        canCraft={checkIngredients(recipe)}
                        isSelected={selectedRecipe?.id === recipe.id}
                        onSelect={onSelectRecipe}
                    />
                ))
            ) : (
                <p>{getText('No recipes available in this category')}</p>
            )}
        </div>
    );
};



// Component for a single recipe item in the list
const RecipeItem: React.FC<{
    recipe: CraftableItem;
    canCraft: boolean;
    isSelected: boolean;
    onSelect: (recipe: CraftableItem) => void;
}> = ({ recipe, canCraft, isSelected, onSelect }) => {
    return (
        <div
            className={`recipe-item ${canCraft ? 'available' : 'unavailable'} ${isSelected ? 'selected' : ''}`}
            onClick={() => onSelect(recipe)}
        >
            <div className="recipe-icon">
                <ItemIcon itemDef={recipe} />
            </div>
            <div className="recipe-info">
                <div className="recipe-name">{recipe.name}</div>
                <div className="recipe-status">
                    {canCraft ? '✅ Available' : '❌ Missing ingredients'}
                </div>
            </div>
        </div>
    );
};
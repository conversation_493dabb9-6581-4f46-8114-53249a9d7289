import React = require("react");

export const ItemIcon = (props: {
    itemDef: {icon: string, multiIcons?: boolean}
    uuid?: string
    maxWidth?: number
    invertColor?: boolean
}) => {
    if (!props.itemDef.icon) {
        console.error("Item icon is undefined", props.itemDef);
        return;
    } else if (props.itemDef.icon.length < 4) {
        return props.itemDef.icon;
    } else if (props.itemDef.multiIcons && props.uuid) {
        // console.log("props.uuid", props.uuid);
        const iconArr = props.itemDef.icon.split(',');
        const part = 16 / iconArr.length;
        const hex =  parseInt(props.uuid.slice(0, 1), 16);
        const index = Math.floor(hex / part);
        // console.log("iconArr[index]", iconArr[index], index, hex, part);
        return <img src={iconArr[index]} className="icon-img" />;
    } else {
        return (
            <div className="icon-img-div" style={props.maxWidth ? {maxWidth: props.maxWidth, maxHeight: props.maxWidth} : null}>
                <img src={props.itemDef.icon} className="icon-img" style={props.invertColor ? {filter: 'invert(1)'} : null} />
            </div>
        );
    }
};

export interface ResourceMetaInfo {
    id: string;
    discovered: boolean;
}

export interface InventoryItemStack {
    uuid: string;
    itemId: string;
    itemDef: Item;
    quantity: number;
    freshness?: number;
    durability?: number;
    buildingsInside?: InventoryItemStack[];
    itemsInGround?: InventoryItemStack[];
}

export interface ResourceType {
    id: string;
    name: string;
    icon: string;
    description: string;
    invActionDesc?: string;
    invIcon?: string;
}

export interface ItemToConsume {
    itemDef: Item;
    quantity: number;
    uuid?: string;
}

export interface Item {
    id: string;
    name: string;
    description: string;
    type: ResourceType;
    icon: string;
    interactions?: { id: string, name: string, icon: string, action: Function }[];
    // storage?: { capacity: number };
    food?: number,
    water?: number
    energy?: number
    health?: number
    sanity?: number
    freshness?: number // in game hours
    useTimeModifier?: number
    rarity?: number;
    isFishingRod?: boolean;
    quantityMod?: number;
    ingredients?: ItemToConsume[];
    time?: number;
    waterVal?: number;
    vegVal?: number;
    fruitVal?: number;
    meatVal?: number;
    sweetVal?: number;
    fishVal?: number;
    isFlammable?: boolean;
    burnTime?: number;
    maxDurability?: number;
    storageCapacity?: number; // number of slots
    weight?: number; // in kg
    canHaveBuildings?: boolean;
    buildingsInside?: CraftableItem[];
}

export interface CraftableItem extends Item {
    ingredients?: ItemToConsume[];
    time?: number;
}

export interface CraftableTool extends CraftableItem {
    durability: number; // how many times it can be used
}

export interface SlotType {
    id: string;
    name: string;
    icon: string;
    description: string;
}

export interface EquipableItem extends CraftableItem {
    slotType: SlotType;
}

export interface BackPack extends EquipableItem {
    storageCapacity: number; // number of slots
}

export interface EdibleItem extends Item {
    food?: number,
    water?: number
    energy?: number
    health?: number
}

export interface Fish extends Item {
    fishVal?: number;
}

export interface Fruit extends Item {
    fruitVal?: number;
}

export interface Vegetable extends Item {
    vegVal?: number;
}

export interface Sweetener extends Item {
    sweetVal?: number;
}

export interface Meat extends Item {
    meatVal?: number;
}

export interface BuildingInteraction {
    id: string;
    name: string;
    icon: string;
    action: (building: PlacedBuilding) => void;
    condition?: (building: PlacedBuilding) => boolean;
    duration?: number;
}

export interface Building {
    id: string;
    name: string;
    description: string;
    icon: string;
    resources: ItemToConsume[];
    interactions: BuildingInteraction[];
    width?: number;
    height?: number;
    multiIcons?: boolean;
    hideInCraftList?: boolean;
}

export interface StorageBuilding extends Building {
    storageCapacity: number; // in kg
}

export interface PlacedBuilding {
    id: string; // uuid
    buildingDef: Building;
    x: number;
    y: number;
    storage?: {
        items: InventoryItemStack[];
        capacity: number;
    };
    isLit?: boolean;
    fireDuration?: number;
}

export interface CookingRecipe {
    id: string;
    name: string;
    description: string;
    icon: string;
    type: ResourceType;
    food: number;
    water: number;
    energy: number;
    health: number;
    requiredWaterVal?: number;
    requiredVegVal?: number;
    requiredFruitVal?: number;
    requiredMeatVal?: number;
    requiredSweetVal?: number;
    requiredFishVal?: number;
    discovered?: boolean;
}

export interface BuildingContextState {
    gridSize: number;
    gridWidth: number;
    gridHeight: number;
}

export interface BuildingContextType {
    state: BuildingContextState;
    placedBuildingsMap: Map<string, PlacedBuilding>;
    selectedBuildingToPlace: Building | null;
    selectBuilding: (building: Building) => void;
    placeBuilding: (x: number, y: number) => void;
    getBuildingDef: (buildingId: string) => Building;
    handleBuildingClick: (building: Building) => void;
    interactionBuilding: Building;
    setShowInteractionMenu: (building: Building) => void;
    cancelPlacement: () => void;
}

export interface Boat extends CraftableItem {
    durability: number;
    maxDurability: number;
    weightCap: number;
    weight: number;
    speed: number;
    waterproof: number;
    insulation: number;
    thermalShield: number;
    ingredients: ItemToConsume[];
}

export interface BoatState {
    currentBoat: Boat | null;
}
